"use client";

import Image from "next/image";
import { useEffect, useRef, useState } from "react";

// Hero Section Component
function HeroSection() {
  const sectionRef = useRef<HTMLElement>(null);
  const scrollProgress = useScrollAnimation(sectionRef);

  return (
    <section ref={sectionRef} className="min-h-screen flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-gray-50 to-white">
      {/* Particle Effects Background */}
      <div className="absolute inset-0 z-0">
        <div className="absolute right-0 top-1/2 -translate-y-1/2 w-1/2 h-full opacity-30">
          <Image
            src="/particles.jpg"
            alt="Particle effects"
            fill
            className="object-cover object-left"
            priority
          />
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 lg:px-8 relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - Text Content */}
          <div className="space-y-8">
            <div className="space-y-5">
              <h1
                className="text-5xl lg:text-2xl font-extralight text-gray-400 leading-tight transform transition-all duration-700 ease-out"
                style={{
                  transform: `translateX(${(1 - scrollProgress) * 100}px)`,
                  opacity: scrollProgress,
                }}
              >
                Ascenda
              </h1>
              <h1
                className="text-5xl lg:text-6xl font-medium text-gray-900 leading-tight transform transition-all duration-700 ease-out"
                style={{
                  transform: `translateX(${(1 - scrollProgress) * 150}px)`,
                  opacity: scrollProgress,
                  transitionDelay: '200ms'
                }}
              >
                Install the Growth Infrastructure
              </h1>
              <p
                className="text-xl lg:text-2xl text-gray-700 leading-relaxed transform transition-all duration-700 ease-out"
                style={{
                  transform: `translateX(${(1 - scrollProgress) * 200}px)`,
                  opacity: scrollProgress,
                  transitionDelay: '400ms'
                }}
              >
                Your Business{" "}
                <span className="font-extralight text-gray-400">
                  Has Been Missing
                </span>
              </p>
            </div>
          </div>

          {/* Right Column - Visual Space for Particles */}
          <div className="hidden lg:block relative">
            <div className="w-full h-96 relative">
              {/* This space is intentionally left for the particle effects background */}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// Plug and Pay Systems Section Component
function PlugAndPaySection() {
  return (
    <section className="min-h-screen bg-gray-100 flex items-center">
      <div className="container mx-auto px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-start">
          {/* Left Column - Visual/Image Space */}
          <div className="bg-blue-50 p-6 lg:p-8 h-180 flex items-center justify-center order-2 lg:order-1">
            {/* Placeholder for visual content */}
          </div>

          {/* Right Column - Content */}
          <div className="space-y-6 order-1 lg:order-2">
            <div className="space-y-4">
              <p className="text-sm font-medium pt-3 text-gray-500 uppercase tracking-wider">
                Ascenda Partners builds
              </p>
              <h2 className="text-4xl lg:text-9xl font-medium text-gray-900 leading-tight">
                PLUG AND PAY SYSTEMS
              </h2>
            </div>

            <div className="space-y-4">
              <p className="text-lg text-gray-600 leading-relaxed">
                <span className="font-medium text-gray-900">
                  that help you generate leads, close more deals, and scale with
                  precision
                </span>
              </p>
              <p className="text-base text-gray-600 italic">
                No fluff. No retainers. Just automated growth machines —
                installed in days.
              </p>
            </div>

            
          </div>
        </div>
      </div>
    </section>
  );
}

// Scroll Animation Hook
function useScrollAnimation(ref: React.RefObject<HTMLElement>) {
  const [scrollProgress, setScrollProgress] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      if (!ref.current) return;

      const rect = ref.current.getBoundingClientRect();
      const windowHeight = window.innerHeight;
      const sectionHeight = rect.height;

      // Calculate how much of the section is visible
      const visibleTop = Math.max(0, windowHeight - rect.top);
      const visibleBottom = Math.max(0, rect.bottom);
      const visibleHeight = Math.min(visibleTop, visibleBottom, sectionHeight);

      // Calculate scroll progress (0 to 1)
      const progress = Math.min(Math.max(visibleHeight / sectionHeight, 0), 1);
      setScrollProgress(progress);
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // Initial call

    return () => window.removeEventListener('scroll', handleScroll);
  }, [ref]);

  return scrollProgress;
}

// Credibility/Promise Bar Section Component
function CredibilitySection() {
  const sectionRef = useRef<HTMLElement>(null);
  const scrollProgress = useScrollAnimation(sectionRef);

  const credibilityItems = [
    "Built by systems architects, not service providers",
    "Powered by AI, automation, and clean workflows",
    "Trusted by consultants, clinics, and scaling businesses",
    "Installed in <14 days. Engineered for scale."
  ];

  return (
    <section ref={sectionRef} className="py-12 bg-white border-t border-gray-200 overflow-hidden">
      <div className="container mx-auto px-6 lg:px-8">
        <div className="flex flex-col items-center space-y-6">
          {credibilityItems.map((item, index) => {
            // Calculate individual item progress with staggered timing
            const itemProgress = Math.max(0, Math.min(1, (scrollProgress - (index * 0.2)) / 0.6));
            const translateX = (1 - itemProgress) * 200; // Start further right
            const opacity = itemProgress;

            return (
              <div
                key={index}
                className="transform transition-all duration-300 ease-out"
                style={{
                  transform: `translateX(${translateX}px)`,
                  opacity: opacity,
                }}
              >
                <p className="text-lg font-medium text-gray-900 text-center">
                  {item}
                </p>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
}

// What We Do Section Component
function WhatWeDoSection() {
  const sectionRef = useRef<HTMLElement>(null);
  const scrollProgress = useScrollAnimation(sectionRef);

  const bulletPoints = [
    { text: "Generate qualified leads", system: "(Kairo)" },
    { text: "Close more sales", system: "(Airo)" },
    { text: "Deliver premium onboarding", system: "(Onboard)" },
    { text: "Increase lifetime value", system: "(Nurture, Retain)" }
  ];

  return (
    <section ref={sectionRef} className="min-h-screen bg-gray-100 flex items-center overflow-hidden">
      <div className="container mx-auto px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-start">
          {/* Left Column - Content */}
          <div className="space-y-6 order-1 lg:order-1">
            <div className="space-y-6">
              <p
                className="text-sm pt-3 font-medium text-gray-500 uppercase tracking-wider transform transition-all duration-700 ease-out"
                style={{
                  transform: `translateX(${(1 - scrollProgress) * 100}px)`,
                  opacity: scrollProgress,
                }}
              >
                We Don't Sell Time
              </p>
              <h2
                className="text-4xl lg:text-7xl font-medium text-gray-900 leading-[1.4] transform transition-all duration-700 ease-out"
                style={{
                  transform: `translateX(${(1 - scrollProgress) * 150}px)`,
                  opacity: scrollProgress,
                  // transitionDelay: '100ms'
                }}
              >
                WE INSTALL SYSTEMS THAT MULTIPLY IT.
              </h2>
            </div>

            <div className="space-y-4">
              <p
                className="text-lg text-gray-600 leading-relaxed transform transition-all duration-700 ease-out"
                style={{
                  transform: `translateX(${(1 - scrollProgress) * 120}px)`,
                  opacity: scrollProgress,
                  // transitionDelay: '400ms'
                }}
              >
                At Ascenda, we don't do retainers, vague strategies, or service
                packages.
              </p>
              <p
                className="text-lg text-gray-600 leading-relaxed transform transition-all duration-700 ease-out"
                style={{
                  transform: `translateX(${(1 - scrollProgress) * 140}px)`,
                  opacity: scrollProgress,
                  // transitionDelay: '500ms'
                }}
              >
                We install modular business systems that are fully automated,
                AI-powered, and designed to help you:
              </p>
            </div>

            <div className="space-y-3 pl-4">
              {bulletPoints.map((point, index) => (
                <div
                  key={index}
                  className="flex items-start gap-3 transform transition-all duration-700 ease-out"
                  style={{
                    transform: `translateX(${(1 - scrollProgress) * 100}px)`,
                    opacity: scrollProgress,
                    // transitionDelay: `${600 + (index * 100)}ms`
                  }}
                >
                  <span className="text-black font-bold">•</span>
                  <span className="text-base text-gray-700">
                    {point.text}{" "}
                    <span className="font-medium">{point.system}</span>
                  </span>
                </div>
              ))}
            </div>

            <div className="space-y-4">
              <p
                className="text-base text-gray-600 italic transform transition-all duration-700 ease-out"
                style={{
                  transform: `translateX(${(1 - scrollProgress) * 80}px)`,
                  opacity: scrollProgress,
                  // transitionDelay: '1000ms'
                }}
              >
                Each system runs in the background — like a revenue engine that
                never stops.
              </p>

              <div className="pt-4">
                <button
                  type="button"
                  className="bg-gray-900 cursor-pointer hover:bg-gray-800 text-white px-8 py-4 font-semibold text-lg transition-all duration-200 border-2 border-gray-900 hover:border-gray-800 transform"
                  style={{
                    transform: `translateX(${(1 - scrollProgress) * 60}px)`,
                    opacity: scrollProgress,
                    // transitionDelay: '1200ms'
                  }}
                >
                  Book a call and we'll show you what system fits you best
                </button>
              </div>
            </div>
          </div>

          {/* Right Column - Visual Space */}
          <div className="bg-blue-50 p-6 lg:p-8 h-180 flex items-center justify-center order-2 lg:order-2">
            {/* Placeholder for system diagram or visual */}
          </div>
        </div>
      </div>
    </section>
  );
}

// Featured System Section Component
function FeaturedSystemSection() {
  return (
    <section className="py-20 bg-white overflow-hidden">
      <div className="container mx-auto px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12">
          {/* KAIRO System Card */}
          <div className="bg-gray-50 p-8 lg:p-10 space-y-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
               style={{
                 animation: 'parallaxFloat1 8s ease-in-out infinite'
               }}>
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <h3 className="text-2xl lg:text-7xl font-medium text-gray-900">
                  KAIRO
                </h3>
              </div>
              <h4 className="text-xl font-medium text-gray-900">
                Your Automated Growth Audit System
              </h4>
              <p className="text-base text-gray-600">
                Turn cold leads into warm calls using a self-diagnosing growth audit — powered by AI.
              </p>
            </div>

            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <span className="text-gray-900 font-bold">•</span>
                <span className="text-sm text-gray-700">Free value first.</span>
              </div>
              <div className="flex items-start gap-3">
                <span className="text-gray-900 font-bold">•</span>
                <span className="text-sm text-gray-700">High intent leads only.</span>
              </div>
              <div className="flex items-start gap-3">
                <span className="text-gray-900 font-bold">•</span>
                <span className="text-sm text-gray-700">Pre-qualifies your pipeline.</span>
              </div>
            </div>

            <div className="pt-4">
              <button
                type="button"
                className="bg-gray-900 cursor-pointer hover:bg-gray-800 text-white px-6 py-3 font-semibold transition-all duration-200 border-2 border-gray-900 hover:border-gray-800"
              >
Run Your Free Audit
              </button>
            </div>
          </div>

          {/* AIRO System Card */}
          <div className="bg-gray-50 p-8 lg:p-10 space-y-6 text-right shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
               style={{
                 animation: 'parallaxFloat2 8s ease-in-out infinite 3s'
               }}>
            <div className="space-y-4">
              <div className="flex items-center gap-3 justify-end">
                <h3 className="text-2xl lg:text-7xl font-medium text-gray-900">
                  AIRO
                </h3>
              </div>
              <h4 className="text-xl font-medium text-gray-900">
                Your AI Sales Prep System
              </h4>
              <p className="text-base text-gray-600">
                Help your sales team close faster by letting AI prep every call, every time.
              </p>
            </div>

            <div className="space-y-3">
              <div className="flex items-start gap-3 justify-end">
                <span className="text-sm text-gray-700">Fully customized CRM logic</span>
                <span className="text-gray-900 font-bold">•</span>
              </div>
              <div className="flex items-start gap-3 justify-end">
                <span className="text-sm text-gray-700">Competitor analysis</span>
                <span className="text-gray-900 font-bold">•</span>
              </div>
              <div className="flex items-start gap-3 justify-end">
                <span className="text-sm text-gray-700">Objection profiles and deal notes auto-generated</span>
                <span className="text-gray-900 font-bold">•</span>
              </div>
            </div>

            <div className="pt-4 flex justify-end">
              <button
                type="button"
                className="bg-gray-900 cursor-pointer hover:bg-gray-800 text-white px-6 py-3 font-semibold transition-all duration-200 border-2 border-gray-900 hover:border-gray-800"
              >
                See How Airo Works
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// How It Works Section Component
function HowItWorksSection() {
  return (
    <section className="py-20 bg-gray-100">
      <div className="container mx-auto px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-7xl text-right font-medium text-gray-800 uppercase leading-tight mb-8">
            Built to Be Installed. Not Managed.
          </h2>
        </div>

        <div className="grid lg:grid-cols-3 gap-8 lg:gap-12 mb-12">
          {/* Step 1 */}
          <div className="space-y-4">
            <div className="bg-white p-8 space-y-4">
              <h3 className="text-xl font-bold text-gray-900">Diagnose</h3>
              <p className="text-base text-gray-600">
                We identify the best-fit system for your business.
              </p>
            </div>
          </div>

          {/* Step 2 */}
          <div className="text-center space-y-4">
            <div className="bg-white p-8 space-y-4">
              <h3 className="text-xl font-bold text-gray-900">Install</h3>
              <p className="text-base text-gray-600">
                Our team builds and customizes it in under 2 weeks.
              </p>
            </div>
          </div>

          {/* Step 3 */}
          <div className="text-center space-y-4">
            <div className="bg-white p-8 space-y-4 text-right">
              <h3 className="text-xl font-bold text-gray-900">Run</h3>
              <p className="text-base text-gray-600">
                The system operates automatically. You monitor the dashboard. We
                handle the rest.
              </p>
            </div>
          </div>
        </div>

        <div className="text-center ">
          <button
            type="button"
            className="bg-gray-900 cursor-pointer hover:bg-gray-800 text-white px-8 py-4 font-semibold text-lg transition-all duration-200 border-2 border-gray-900 hover:border-gray-800"
          >
            Book Your System Assessment
          </button>
        </div>
      </div>
    </section>
  );
}

// Why Ascenda Differentiator Section Component
function WhyAscendaSection() {
  return (
    <section className="py-20 bg-white border-t border-gray-200">
      <div className="container mx-auto px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-8">
            Ascenda vs. The Rest
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div className="text-center space-y-3">
            <div className="text-2xl text-green-600">✔</div>
            <h3 className="font-semibold text-gray-900">Productized AI Systems</h3>
          </div>

          <div className="text-center space-y-3">
            <div className="text-2xl text-green-600">✔</div>
            <h3 className="font-semibold text-gray-900">Built in days, not months</h3>
          </div>

          <div className="text-center space-y-3">
            <div className="text-2xl text-green-600">✔</div>
            <h3 className="font-semibold text-gray-900">Designed to scale automatically</h3>
          </div>

          <div className="text-center space-y-3">
            <div className="text-2xl text-green-600">✔</div>
            <h3 className="font-semibold text-gray-900">Boutique, high-end experience</h3>
          </div>
        </div>
      </div>
    </section>
  );
}

// Testimonials Section Component
function TestimonialsSection() {
  const sectionRef = useRef<HTMLElement>(null);
  const scrollProgress = useScrollAnimation(sectionRef);

  const testimonials = [
    {
      quote: "Since we installed Airo, our close rate went up 33% — without hiring anyone new.",
      name: "Sarah T.",
      title: "Consultant, Dubai"
    },
    {
      quote: "Kairo gave us 40 leads in 3 weeks — and 9 booked calls. All automated.",
      name: "David R.",
      title: "Agency Owner"
    }
  ];

  return (
    <section ref={sectionRef} className="py-20 bg-gray-100 overflow-hidden">
      <div className="container mx-auto px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12">
          {testimonials.map((testimonial, index) => (
            <div
              key={index}
              className="bg-white p-8 lg:p-10 space-y-6 transform transition-all duration-700 ease-out"
              style={{
                transform: `translateX(${(1 - scrollProgress) * (index === 0 ? 150 : -150)}px)`,
                opacity: scrollProgress,
                transitionDelay: `${index * 300}ms`
              }}
            >
              <p className="text-lg text-gray-700 italic leading-relaxed">
                "{testimonial.quote}"
              </p>
              <div className="border-t pt-4">
                <p className="font-semibold text-gray-900">{testimonial.name}</p>
                <p className="text-sm text-gray-600">{testimonial.title}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

// Final CTA Section Component
function FinalCTASection() {
  return (
    <section className="min-h-screen bg-gray-900 flex items-center">
      <div className="container mx-auto px-6 lg:px-8 text-center">
        <div className="max-w-4xl mx-auto space-y-8">
          <h2 className="text-4xl lg:text-6xl font-bold text-white leading-tight">
            Let's Turn Your Business Into a Machine
          </h2>

          <p className="text-xl text-gray-300 leading-relaxed max-w-3xl mx-auto">
            If you're tired of manual hustle, inconsistent leads, and bloated agencies…
            It's time to install real growth infrastructure.
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center pt-8">
            <button
              type="button"
              className="bg-white cursor-pointer hover:bg-gray-100 text-gray-900 px-8 py-4 font-semibold text-lg transition-all duration-200 border-2 border-white hover:border-gray-100"
            >
             Run Your Free Growth Audit
            </button>
            <button
              type="button"
              className="bg-transparent cursor-pointer hover:bg-white text-white hover:text-gray-900 px-8 py-4 font-semibold text-lg transition-all duration-200 border-2 border-white"
            >
            Book a Discovery Call
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}

export default function Home() {
  return (
    <main>
      <HeroSection />
      <PlugAndPaySection />
      <CredibilitySection />
      <WhatWeDoSection />
      <FeaturedSystemSection />
      <HowItWorksSection />
      <WhyAscendaSection />
      <TestimonialsSection />
      <FinalCTASection />
    </main>
  );
}
