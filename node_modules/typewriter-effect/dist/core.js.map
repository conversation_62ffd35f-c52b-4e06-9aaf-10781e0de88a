{"version": 3, "file": "core.js", "mappings": "CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,aAAc,GAAIH,GACC,iBAAZC,QACdA,QAAoB,WAAID,IAExBD,EAAiB,WAAIC,GACtB,CATD,CASmB,oBAATK,KAAuBA,KAAOC,MAAM,I,4BCF9C,IAPA,IAAIC,EAAM,EAAQ,MACdR,EAAyB,oBAAXS,OAAyB,EAAAC,EAASD,OAChDE,EAAU,CAAC,MAAO,UAClBC,EAAS,iBACTC,EAAMb,EAAK,UAAYY,GACvBE,EAAMd,EAAK,SAAWY,IAAWZ,EAAK,gBAAkBY,GAEpDG,EAAI,GAAIF,GAAOE,EAAIJ,EAAQK,OAAQD,IACzCF,EAAMb,EAAKW,EAAQI,GAAK,UAAYH,GACpCE,EAAMd,EAAKW,EAAQI,GAAK,SAAWH,IAC5BZ,EAAKW,EAAQI,GAAK,gBAAkBH,GAI7C,IAAIC,IAAQC,EAAK,CACf,IAAIG,EAAO,EACPC,EAAK,EACLC,EAAQ,GACRC,EAAgB,IAAO,GAE3BP,EAAM,SAASQ,GACb,GAAoB,IAAjBF,EAAMH,OAAc,CACrB,IAAIM,EAAOd,IACPe,EAAOC,KAAKC,IAAI,EAAGL,GAAiBE,EAAOL,IAC/CA,EAAOM,EAAOD,EACdI,YAAW,WACT,IAAIC,EAAKR,EAAMS,MAAM,GAIrBT,EAAMH,OAAS,EACf,IAAI,IAAID,EAAI,EAAGA,EAAIY,EAAGX,OAAQD,IAC5B,IAAIY,EAAGZ,GAAGc,UACR,IACEF,EAAGZ,GAAGM,SAASJ,EACjB,CAAE,MAAMa,GACNJ,YAAW,WAAa,MAAMI,CAAE,GAAG,EACrC,CAGN,GAAGN,KAAKO,MAAMR,GAChB,CAMA,OALAJ,EAAMa,KAAK,CACTC,SAAUf,EACVG,SAAUA,EACVQ,WAAW,IAENX,CACT,EAEAJ,EAAM,SAASmB,GACb,IAAI,IAAIlB,EAAI,EAAGA,EAAII,EAAMH,OAAQD,IAC5BI,EAAMJ,GAAGkB,SAAWA,IACrBd,EAAMJ,GAAGc,WAAY,EAG3B,CACF,CAEA1B,EAAOD,QAAU,SAASgC,GAIxB,OAAOrB,EAAIsB,KAAKnC,EAAMkC,EACxB,EACA/B,EAAOD,QAAQkC,OAAS,WACtBtB,EAAIuB,MAAMrC,EAAMsC,UAClB,EACAnC,EAAOD,QAAQqC,SAAW,SAASC,GAC5BA,IACHA,EAASxC,GAEXwC,EAAOC,sBAAwB5B,EAC/B2B,EAAOE,qBAAuB5B,CAChC,C,oBCzEA,WACE,IAAI6B,EAAgBC,EAAQC,EAAUC,EAAgBC,EAAcC,EAExC,oBAAhBC,aAA+C,OAAhBA,aAAyBA,YAAYzC,IAC9EL,EAAOD,QAAU,WACf,OAAO+C,YAAYzC,KACrB,EAC6B,oBAAZ0C,SAAuC,OAAZA,SAAqBA,QAAQN,QACzEzC,EAAOD,QAAU,WACf,OAAQyC,IAAmBI,GAAgB,GAC7C,EACAH,EAASM,QAAQN,OAMjBE,GALAH,EAAiB,WACf,IAAIQ,EAEJ,OAAe,KADfA,EAAKP,KACK,GAAWO,EAAG,EAC1B,KAEAH,EAA4B,IAAnBE,QAAQE,SACjBL,EAAeD,EAAiBE,GACvBK,KAAK7C,KACdL,EAAOD,QAAU,WACf,OAAOmD,KAAK7C,MAAQqC,CACtB,EACAA,EAAWQ,KAAK7C,QAEhBL,EAAOD,QAAU,WACf,OAAO,IAAImD,MAAOC,UAAYT,CAChC,EACAA,GAAW,IAAIQ,MAAOC,UAGzB,GAAEnB,KAAK5B,K,GChCJgD,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBE,IAAjBD,EACH,OAAOA,EAAaxD,QAGrB,IAAIC,EAASoD,EAAyBE,GAAY,CAGjDvD,QAAS,CAAC,GAOX,OAHA0D,EAAoBH,GAAUtB,KAAKhC,EAAOD,QAASC,EAAQA,EAAOD,QAASsD,GAGpErD,EAAOD,OACf,CCrBAsD,EAAoBK,EAAK1D,IACxB,IAAI2D,EAAS3D,GAAUA,EAAO4D,WAC7B,IAAO5D,EAAiB,QACxB,IAAM,EAEP,OADAqD,EAAoBQ,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,CAAM,ECLdN,EAAoBQ,EAAI,CAAC9D,EAASgE,KACjC,IAAI,IAAIC,KAAOD,EACXV,EAAoBY,EAAEF,EAAYC,KAASX,EAAoBY,EAAElE,EAASiE,IAC5EE,OAAOC,eAAepE,EAASiE,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,IAE1E,ECNDX,EAAoB9C,EAAI,WACvB,GAA0B,iBAAf+D,WAAyB,OAAOA,WAC3C,IACC,OAAOlE,MAAQ,IAAImE,SAAS,cAAb,EAChB,CAAE,MAAO5C,GACR,GAAsB,iBAAXrB,OAAqB,OAAOA,MACxC,CACA,CAPuB,GCAxB+C,EAAoBY,EAAI,CAACO,EAAKC,IAAUP,OAAOQ,UAAUC,eAAe3C,KAAKwC,EAAKC,G,gFCYlF,QALiC,SAACG,GAEhC,OADe,IAAIC,OAAO,mBACZC,KAAKF,EACrB,ECCA,EAJyB,SAACG,EAAKzD,GAC7B,OAAOD,KAAK2D,MAAM3D,KAAK4D,UAAY3D,EAAMyD,EAAM,IAAMA,CACvD,ECTO,IAAMG,EACK,iBADLA,EAEO,mBAFPA,EAGC,aAHDA,EAIe,2BAJfA,EAKA,YALAA,EAMI,gBANJA,EAOW,uBAPXA,EASU,sBATVA,EAUG,eAVHA,EAWI,gBAXJA,EAYG,eAGHC,EACD,W,4jECouBZ,QAvuBgB,WAoCd,SAAAC,EAAYC,EAAWC,GAAS,IAAAC,EAAA,KAC9B,G,4FAD8BC,CAAA,KAAAJ,GAAAK,EAAA,aAnCxB,CACNC,gBAAiB,KACjBC,cAAe,KACfC,WAAY,KACZC,WAAY,GACZC,UAAW,KACXC,iBAAiB,EACjBC,oBAAqB,GACrBC,aAAc,GACdC,aAAc,GACdC,eAAgB,KAChBC,SAAU,CACRf,UAAW,KACXgB,QAASC,SAASC,cAAc,QAChCC,OAAQF,SAASC,cAAc,WAElCd,EAAA,eAES,CACRgB,QAAS,KACTD,OAAQ,IACRE,MAAO,UACPC,SAAU,KACVC,YAAa,UACbC,MAAM,EACNC,WAAW,EACXC,SAAS,EACTC,eAAe,EACfC,iBAAkB,sBAClBC,gBAAiB,qBACjBC,eAAgB,KAChBC,iBAAkB,KAClBC,aAAc,OA8ChB5B,EAAA,4BAMsB,WAChBF,EAAK+B,MAAMlB,SAASf,YAIxBE,EAAK+B,MAAMlB,SAASC,QAAQkB,UAAYhC,EAAKD,QAAQ2B,iBACrD1B,EAAK+B,MAAMlB,SAASI,OAAOe,UAAYhC,EAAKD,QAAQ4B,gBAEpD3B,EAAK+B,MAAMlB,SAASI,OAAOgB,UAAYjC,EAAKD,QAAQkB,OACpDjB,EAAK+B,MAAMlB,SAASf,UAAUmC,UAAY,GAE1CjC,EAAK+B,MAAMlB,SAASf,UAAUoC,YAAYlC,EAAK+B,MAAMlB,SAASC,SAC9Dd,EAAK+B,MAAMlB,SAASf,UAAUoC,YAAYlC,EAAK+B,MAAMlB,SAASI,QAChE,IAEAf,EAAA,cAGQ,WAIN,OAHAF,EAAK+B,MAAMvB,iBAAkB,EAC7BR,EAAKmC,eAEEnC,CACT,IAEAE,EAAA,cAKQ,WAGN,OAFAF,EAAK+B,MAAMvB,iBAAkB,EAEtBR,CACT,IAEAE,EAAA,aAKO,WAML,OALGF,EAAK+B,MAAMxB,aACZ6B,EAAAA,EAAAA,QAAUpC,EAAK+B,MAAMxB,WACrBP,EAAK+B,MAAMxB,UAAY,MAGlBP,CACT,IAEAE,EAAA,iBAQW,SAACmC,GAGV,OAFArC,EAAKsC,gBAAgB3C,EAAuB,CAAE0C,GAAAA,IAEvCrC,CACT,IAEAE,EAAA,0BAQoB,WAClB,MAAmC,iBAAzBF,EAAKD,QAAQmB,SACrBlB,EAAKuC,WAAWvC,EAAKD,QAAQmB,SAC1BE,SAASpB,EAAKD,QAAQqB,UAClBpB,IAGTA,EAAKD,QAAQmB,QAAQsB,SAAQ,SAAAnD,GAC3BW,EAAKuC,WAAWlD,GACb+B,SAASpB,EAAKD,QAAQqB,UACtBqB,UAAUzC,EAAKD,QAAQsB,YAC5B,IAEOrB,EACT,IAEAE,EAAA,mBASa,SAACb,GAAwB,IAAhBqD,EAAI9F,UAAAtB,OAAA,QAAA2C,IAAArB,UAAA,GAAAA,UAAA,GAAG,KAC3B,GAAG+F,EAAyBtD,GAC1B,OAAOW,EAAK4C,kBAAkBvD,EAAQqD,GAGxC,GAAGrD,EAAQ,CACT,IAAQuC,GAAmB5B,EAAKD,SAAW,CAAC,GAApC6B,eACFiB,EAAuC,mBAAnBjB,EAAgCA,EAAevC,GAAUA,EAAOyD,MAAM,IAChG9C,EAAK+C,eAAeF,EAAYH,EAClC,CAEA,OAAO1C,CACT,IAEAE,EAAA,oBASc,SAACb,GAAwB,IAAhBqD,EAAI9F,UAAAtB,OAAA,QAAA2C,IAAArB,UAAA,GAAAA,UAAA,GAAG,KAC5B,OAAG+F,EAAyBtD,GACnBW,EAAK4C,kBAAkBvD,EAAQqD,GAAM,IAG3CrD,GACDW,EAAKsC,gBAAgB3C,EAA0B,CAAEqD,UAAW3D,EAAQqD,KAAAA,IAG/D1C,EACT,IAEAE,EAAA,0BASoB,SAACb,GAA2C,IAAnC4D,EAAUrG,UAAAtB,OAAA,QAAA2C,IAAArB,UAAA,GAAAA,UAAA,GAAG,KAAMsG,EAAWtG,UAAAtB,OAAA,EAAAsB,UAAA,QAAAqB,EACnDkF,ECtOsB,SAAC9D,GAC/B,IAAM+D,EAAMrC,SAASC,cAAc,OAEnC,OADAoC,EAAInB,UAAY5C,EACT+D,EAAID,UACb,CDkOuBE,CAAwBhE,GAE3C,GAAG8D,EAAW7H,OAAS,EACrB,IAAI,IAAID,EAAI,EAAGA,EAAI8H,EAAW7H,OAAQD,IAAK,CACzC,IAAMqH,EAAOS,EAAW9H,GAClBiI,EAAWZ,EAAKT,UAEnBS,GAA0B,IAAlBA,EAAKa,UAEdb,EAAKT,UAAY,GAGjBjC,EAAKsC,gBAAgB3C,EAAkC,CACrD+C,KAAAA,EACAO,WAAAA,IAGAC,EAAclD,EAAKwD,YAAYF,EAAUZ,GAAS1C,EAAKuC,WAAWe,EAAUZ,IAE3EA,EAAKe,cACNP,EAAclD,EAAKwD,YAAYd,EAAKe,YAAaR,GAAejD,EAAKuC,WAAWG,EAAKe,YAAaR,GAGxG,CAGF,OAAOjD,CACT,IAEAE,EAAA,kBAOY,WAAuB,IAAtBwD,EAAK9G,UAAAtB,OAAA,QAAA2C,IAAArB,UAAA,GAAAA,UAAA,GAAG,UAEnB,OADAoD,EAAKsC,gBAAgB3C,EAAwB,CAAE+D,MAAAA,IACxC1D,CACT,IAEAE,EAAA,0BAQoB,SAACwD,GACnB,IAAIA,EACF,MAAM,IAAIC,MAAM,iCAKlB,OAFA3D,EAAKsC,gBAAgB3C,EAAiC,CAAE+D,MAAAA,IAEjD1D,CACT,IAEAE,EAAA,oBAQc,SAACiB,GACb,IAAIA,EACF,MAAM,IAAIwC,MAAM,0BAKlB,OAFA3D,EAAKsC,gBAAgB3C,EAA0B,CAAEwB,MAAAA,IAE1CnB,CACT,IAEAE,EAAA,qBAQe,SAACe,GACd,IAAIA,EACF,MAAM,IAAI0C,MAAM,2BAKlB,OAFA3D,EAAKsC,gBAAgB3C,EAA2B,CAAEsB,OAAAA,IAE3CjB,CACT,IAEAE,EAAA,oBAQc,SAAC0D,GACb,IAAIA,EACF,MAAM,IAAID,MAAM,+CAGlB,IAAI,IAAItI,EAAI,EAAGA,EAAIuI,EAAQvI,IACzB2E,EAAKsC,gBAAgB3C,GAGvB,OAAOK,CACT,IAEAE,EAAA,qBASe,SAAC2D,EAAIC,GAClB,IAAID,GAAoB,mBAAPA,EACf,MAAM,IAAIF,MAAM,+BAKlB,OAFA3D,EAAKsC,gBAAgB3C,EAA2B,CAAEkE,GAAAA,EAAIC,QAAAA,IAE/C9D,CACT,IAEAE,EAAA,uBASiB,SAAC2C,GAA4B,IAAhBH,EAAI9F,UAAAtB,OAAA,QAAA2C,IAAArB,UAAA,GAAAA,UAAA,GAAG,KACnC,IAAIiG,IAAekB,MAAMC,QAAQnB,GAC/B,MAAM,IAAIc,MAAM,+BAOlB,OAJAd,EAAWL,SAAQ,SAAAQ,GACjBhD,EAAKsC,gBAAgB3C,EAA4B,CAAEqD,UAAAA,EAAWN,KAAAA,GAChE,IAEO1C,CACT,IAEAE,EAAA,yBAQmB,SAAC2C,GAClB,IAAIA,IAAekB,MAAMC,QAAQnB,GAC/B,MAAM,IAAIc,MAAM,+BAOlB,OAJAd,EAAWL,SAAQ,WACjBxC,EAAKsC,gBAAgB3C,EACvB,IAEOK,CACT,IAEAE,EAAA,wBAUkB,SAAC+D,EAAWC,GAA+B,IAApBC,EAAOvH,UAAAtB,OAAA,QAAA2C,IAAArB,UAAA,IAAAA,UAAA,GAC9C,OAAOoD,EAAKoE,wBACVH,EACAC,EACAC,EACA,aAEJ,IAEAjE,EAAA,8BAUwB,SAAC+D,EAAWC,GAA+B,IAApBC,EAAOvH,UAAAtB,OAAA,QAAA2C,IAAArB,UAAA,IAAAA,UAAA,GAGpD,OAFiBoD,EAAKD,QAAduB,KAMDtB,EAAKoE,wBACVH,EACAC,EACAC,EACA,uBAPOnE,CASX,IAEAE,EAAA,gCAW0B,SAAC+D,EAAWC,GAAyC,IAA9BC,EAAOvH,UAAAtB,OAAA,QAAA2C,IAAArB,UAAA,IAAAA,UAAA,GAAUyH,EAAQzH,UAAAtB,OAAA,EAAAsB,UAAA,QAAAqB,EAClEqG,EAAY,CAChBL,UAAAA,EACAC,UAAWA,GAAa,CAAC,GAe3B,OAXElE,EAAK+B,MAAMsC,GADVF,EACsB,CACrBG,GAASC,OAAAC,EACNxE,EAAK+B,MAAMsC,KAGO,GAAHE,OAAAC,EACfxE,EAAK+B,MAAMsC,IAAS,CACvBC,IAIGtE,CACT,IAEAE,EAAA,qBAKe,WACTF,EAAK+B,MAAM3B,gBACbJ,EAAK+B,MAAM3B,cAAgBzC,KAAK7C,OAIlC,IAAM2J,EAAU9G,KAAK7C,MACf4J,EAAQD,EAAUzE,EAAK+B,MAAM3B,cAEnC,IAAIJ,EAAK+B,MAAMzB,WAAWhF,OAAQ,CAChC,IAAI0E,EAAKD,QAAQuB,KACf,OAIFtB,EAAK+B,MAAMzB,WAAUkE,EAAOxE,EAAK+B,MAAMrB,cACvCV,EAAK+B,MAAMrB,aAAe,GAC1BV,EAAKD,QAAO4E,EAAA,GAAO3E,EAAK+B,MAAMnB,eAChC,CAMA,GAHAZ,EAAK+B,MAAMxB,UAAYpF,IAAI6E,EAAKmC,eAG7BnC,EAAK+B,MAAMvB,gBAAd,CAKA,GAAGR,EAAK+B,MAAM1B,WAAY,CAExB,GAAGoE,EAAUzE,EAAK+B,MAAM1B,WACtB,OAIFL,EAAK+B,MAAM1B,WAAa,IAC1B,CAGA,IAMIc,EANEb,EAAUkE,EAAOxE,EAAK+B,MAAMzB,YAG5BsE,EAAetE,EAAWuE,QAgBhC,KAAGH,IALDvD,EAHAyD,EAAaX,YAActE,GAC3BiF,EAAaX,YAActE,EAEU,YAA7BK,EAAKD,QAAQsB,YAA4ByD,EAAiB,GAAI,IAAM9E,EAAKD,QAAQsB,YAE1D,YAAvBrB,EAAKD,QAAQoB,MAAsB2D,EAAiB,IAAK,KAAO9E,EAAKD,QAAQoB,QAGvF,CAKA,IAAQ8C,EAAyBW,EAAzBX,UAAWC,EAAcU,EAAdV,UAKnB,OAHAlE,EAAK+E,aAAa,CAAEH,aAAAA,EAAc7C,MAAO/B,EAAK+B,MAAOZ,MAAAA,IAG9C8C,GACL,KAAKtE,EACL,KAAKA,EACH,IAAQqD,EAAoBkB,EAApBlB,UAAWN,EAASwB,EAATxB,KACbsC,EAAWjE,SAASkE,eAAejC,GAErCkC,EAAgBF,EAEjBhF,EAAKD,QAAQ8B,kBAA6D,mBAAlC7B,EAAKD,QAAQ8B,mBACtDqD,EAAgBlF,EAAKD,QAAQ8B,iBAAiBmB,EAAWgC,IAGxDE,IACExC,EACDA,EAAKR,YAAYgD,GAEjBlF,EAAK+B,MAAMlB,SAASC,QAAQoB,YAAYgD,IAI5ClF,EAAK+B,MAAMpB,aAAe,GAAH4D,OAAAC,EAClBxE,EAAK+B,MAAMpB,cAAY,CAC1B,CACEwE,KD3jBC,YC4jBDnC,UAAAA,EACAN,KAAMwC,KAIV,MAGF,KAAKvF,EACHW,EAAW8E,QAAQ,CACjBnB,UAAWtE,EACXuE,UAAW,CAAEmB,uBAAuB,KAEtC,MAGF,KAAK1F,EACH,IAAQ0C,EAAOuC,EAAaV,UAApB7B,GACRrC,EAAK+B,MAAM1B,WAAa1C,KAAK7C,MAAQwK,SAASjD,GAC9C,MAGF,KAAK1C,EACH,IAAA4F,EAAwBX,EAAaV,UAA7BL,EAAE0B,EAAF1B,GAAIC,EAAOyB,EAAPzB,QAEZD,EAAGpH,KAAKqH,EAAS,CACfjD,SAAUb,EAAK+B,MAAMlB,WAGvB,MAGF,KAAKlB,EACH,IAAA6F,EAA6BZ,EAAaV,UAAlCxB,EAAI8C,EAAJ9C,KAAMO,EAAUuC,EAAVvC,WAEVA,EAGFA,EAAWf,YAAYQ,GAFvB1C,EAAK+B,MAAMlB,SAASC,QAAQoB,YAAYQ,GAK1C1C,EAAK+B,MAAMpB,aAAe,GAAH4D,OAAAC,EAClBxE,EAAK+B,MAAMpB,cAAY,CAC1B,CACEwE,KAAMvF,EACN8C,KAAAA,EACAO,WAAYA,GAAcjD,EAAK+B,MAAMlB,SAASC,WAGlD,MAGF,KAAKnB,EACH,IAAQgB,EAAiBX,EAAK+B,MAAtBpB,aACA+C,EAAUQ,EAAVR,MACF+B,EAAsB,GAGzB/B,GACD+B,EAAoBnJ,KAAK,CACvB2H,UAAWtE,EACXuE,UAAW,CAAER,MAAAA,EAAOgC,MAAM,KAI9B,IAAI,IAAIrK,EAAI,EAAGC,EAASqF,EAAarF,OAAQD,EAAIC,EAAQD,IACvDoK,EAAoBnJ,KAAK,CACvB2H,UAAWtE,EACXuE,UAAW,CAAEmB,uBAAuB,KAKrC3B,GACD+B,EAAoBnJ,KAAK,CACvB2H,UAAWtE,EACXuE,UAAW,CAAER,MAAO1D,EAAKD,QAAQsB,YAAaqE,MAAM,KAIxDpF,EAAW8E,QAAOzI,MAAlB2D,EAAsBmF,GAEtB,MAGF,KAAK9F,EACH,IAAQ0F,EAA0BT,EAAaV,UAAvCmB,sBAER,GAAGrF,EAAK+B,MAAMpB,aAAarF,OAAQ,CACjC,IAAAqK,EAAkC3F,EAAK+B,MAAMpB,aAAaiF,MAAlDT,EAAIQ,EAAJR,KAAMzC,EAAIiD,EAAJjD,KAAMM,EAAS2C,EAAT3C,UAEjBhD,EAAKD,QAAQ+B,cAAqD,mBAA9B9B,EAAKD,QAAQ+B,cAClD9B,EAAKD,QAAQ+B,aAAa,CACxBY,KAAAA,EACAM,UAAAA,IAIDN,GACDA,EAAKO,WAAW4C,YAAYnD,GAI3ByC,IAASvF,GAA+ByF,GACzC/E,EAAW8E,QAAQ,CACjBnB,UAAWtE,EACXuE,UAAW,CAAC,GAGlB,CACA,MAGF,KAAKvE,EACHK,EAAKD,QAAQsB,YAAcuD,EAAaV,UAAUR,MAClD,MAGF,KAAK/D,EACHK,EAAKD,QAAQoB,MAAQyD,EAAaV,UAAU/C,MAC5C,MAGF,KAAKxB,EACHK,EAAKD,QAAQkB,OAAS2D,EAAaV,UAAUjD,OAC7CjB,EAAK+B,MAAMlB,SAASI,OAAOgB,UAAY2C,EAAaV,UAAUjD,OAU/DjB,EAAKD,QAAQuB,OAEZsD,EAAaX,YAActE,GACzBiF,EAAaV,WAAaU,EAAaV,UAAUwB,OAEnD1F,EAAK+B,MAAMrB,aAAe,GAAH6D,OAAAC,EAClBxE,EAAK+B,MAAMrB,cAAY,CAC1BkE,MAMN5E,EAAK+B,MAAMzB,WAAaA,EAGxBN,EAAK+B,MAAM3B,cAAgBqE,CAvL3B,CAnCA,CA2NF,IAnrBK3E,EACD,GAAwB,iBAAdA,EAAwB,CAChC,IAAMgG,EAAmB/E,SAASgF,cAAcjG,GAEhD,IAAIgG,EACF,MAAM,IAAInC,MAAM,oCAGlB9I,KAAKkH,MAAMlB,SAASf,UAAYgG,CAClC,MACEjL,KAAKkH,MAAMlB,SAASf,UAAYA,EAIjCC,IACDlF,KAAKkF,QAAO4E,EAAAA,EAAA,GACP9J,KAAKkF,SACLA,IAKPlF,KAAKkH,MAAMnB,eAAc+D,EAAA,GAAQ9J,KAAKkF,SAEtClF,KAAKmL,MACP,C,QAsqBC,O,EAtqBAnG,G,EAAA,EAAApB,IAAA,OAAAwH,MAED,WEvEgB,IAACC,EACXC,EFuEJtL,KAAKuL,sBACLvL,KAAKyH,gBAAgB3C,EAA2B,CAAEsB,OAAQpG,KAAKkF,QAAQkB,SAAU,GACjFpG,KAAKyH,gBAAgB3C,EAAwB,MAAM,IAEhD5E,QAAWA,OAAOsL,kCAAqCxL,KAAKkF,QAAQ0B,gBE5ExDyE,EHcG,wRGbdC,EAAapF,SAASC,cAAc,UAC/BkB,YAAYnB,SAASkE,eAAeiB,IAC/CnF,SAASuF,KAAKpE,YAAYiE,GF2EtBpL,OAAOsL,kCAAmC,IAGd,IAA3BxL,KAAKkF,QAAQwB,WAAsB1G,KAAKkF,QAAQmB,SACjDrG,KAAK0L,oBAAoBC,OAE7B,GAAC,CAAA/H,IAAA,eAAAwH,MAmpBD,SAAaQ,GACR5L,KAAKkF,QAAQyB,SACdkF,QAAQC,IAAIF,EAEhB,M,oEAAC5G,CAAA,CApuBa,E", "sources": ["webpack://Typewriter/webpack/universalModuleDefinition", "webpack://Typewriter/./node_modules/raf/index.js", "webpack://Typewriter/./node_modules/performance-now/lib/performance-now.js", "webpack://Typewriter/webpack/bootstrap", "webpack://Typewriter/webpack/runtime/compat get default export", "webpack://Typewriter/webpack/runtime/define property getters", "webpack://Typewriter/webpack/runtime/global", "webpack://Typewriter/webpack/runtime/hasOwnProperty shorthand", "webpack://Typewriter/./src/utils/does-string-contain-html-tag.js", "webpack://Typewriter/./src/utils/get-random-integer.js", "webpack://Typewriter/./src/core/constants.js", "webpack://Typewriter/./src/core/Typewriter.js", "webpack://Typewriter/./src/utils/get-dom-element-from-string.js", "webpack://Typewriter/./src/utils/add-styles.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"Typewriter\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Typewriter\"] = factory();\n\telse\n\t\troot[\"Typewriter\"] = factory();\n})(typeof self !== 'undefined' ? self : this, () => {\nreturn ", "var now = require('performance-now')\n  , root = typeof window === 'undefined' ? global : window\n  , vendors = ['moz', 'webkit']\n  , suffix = 'AnimationFrame'\n  , raf = root['request' + suffix]\n  , caf = root['cancel' + suffix] || root['cancelRequest' + suffix]\n\nfor(var i = 0; !raf && i < vendors.length; i++) {\n  raf = root[vendors[i] + 'Request' + suffix]\n  caf = root[vendors[i] + 'Cancel' + suffix]\n      || root[vendors[i] + 'CancelRequest' + suffix]\n}\n\n// Some versions of FF have rAF but not cAF\nif(!raf || !caf) {\n  var last = 0\n    , id = 0\n    , queue = []\n    , frameDuration = 1000 / 60\n\n  raf = function(callback) {\n    if(queue.length === 0) {\n      var _now = now()\n        , next = Math.max(0, frameDuration - (_now - last))\n      last = next + _now\n      setTimeout(function() {\n        var cp = queue.slice(0)\n        // Clear queue here to prevent\n        // callbacks from appending listeners\n        // to the current frame's queue\n        queue.length = 0\n        for(var i = 0; i < cp.length; i++) {\n          if(!cp[i].cancelled) {\n            try{\n              cp[i].callback(last)\n            } catch(e) {\n              setTimeout(function() { throw e }, 0)\n            }\n          }\n        }\n      }, Math.round(next))\n    }\n    queue.push({\n      handle: ++id,\n      callback: callback,\n      cancelled: false\n    })\n    return id\n  }\n\n  caf = function(handle) {\n    for(var i = 0; i < queue.length; i++) {\n      if(queue[i].handle === handle) {\n        queue[i].cancelled = true\n      }\n    }\n  }\n}\n\nmodule.exports = function(fn) {\n  // Wrap in a new function to prevent\n  // `cancel` potentially being assigned\n  // to the native rAF function\n  return raf.call(root, fn)\n}\nmodule.exports.cancel = function() {\n  caf.apply(root, arguments)\n}\nmodule.exports.polyfill = function(object) {\n  if (!object) {\n    object = root;\n  }\n  object.requestAnimationFrame = raf\n  object.cancelAnimationFrame = caf\n}\n", "// Generated by CoffeeScript 1.12.2\n(function() {\n  var getNanoSeconds, hrtime, loadTime, moduleLoadTime, nodeLoadTime, upTime;\n\n  if ((typeof performance !== \"undefined\" && performance !== null) && performance.now) {\n    module.exports = function() {\n      return performance.now();\n    };\n  } else if ((typeof process !== \"undefined\" && process !== null) && process.hrtime) {\n    module.exports = function() {\n      return (getNanoSeconds() - nodeLoadTime) / 1e6;\n    };\n    hrtime = process.hrtime;\n    getNanoSeconds = function() {\n      var hr;\n      hr = hrtime();\n      return hr[0] * 1e9 + hr[1];\n    };\n    moduleLoadTime = getNanoSeconds();\n    upTime = process.uptime() * 1e9;\n    nodeLoadTime = moduleLoadTime - upTime;\n  } else if (Date.now) {\n    module.exports = function() {\n      return Date.now() - loadTime;\n    };\n    loadTime = Date.now();\n  } else {\n    module.exports = function() {\n      return new Date().getTime() - loadTime;\n    };\n    loadTime = new Date().getTime();\n  }\n\n}).call(this);\n\n//# sourceMappingURL=performance-now.js.map\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "/**\n * Check if a string contains a HTML tag or not\n * \n * @param {String} string String to check for HTML tag\n * @return {Boolean} True|False\n * \n */\nconst doesStringContainHTMLTag = (string) => {\n  const regexp = new RegExp(/<[a-z][\\s\\S]*>/i);\n  return regexp.test(string);\n};\n\nexport default doesStringContainHTMLTag;", "/**\n * Return a random integer between min/max values\n * \n * @param {Number} min Minimum number to generate\n * @param {Number} max Maximum number to generate\n * <AUTHOR> <<EMAIL>>\n */\nconst getRandomInteger = (min, max) => {\n  return Math.floor(Math.random() * (max - min + 1)) + min;\n}\n\nexport default getRandomInteger;", "export const EVENT_NAMES = {\n  TYPE_CHARACTER: 'TYPE_CHARACTER',\n  REMOVE_CHARACTER: 'REMOVE_CHARACTER',\n  REMOVE_ALL: 'REMOVE_ALL',\n  REMOVE_LAST_VISIBLE_NODE: 'REMOVE_LAST_VISIBLE_NODE',\n  PAUSE_FOR: 'PAUSE_FOR',\n  CALL_FUNCTION: 'CALL_FUNCTION',\n  ADD_HTML_TAG_ELEMENT: 'ADD_HTML_TAG_ELEMENT',\n  REMOVE_HTML_TAG_ELEMENT: 'REMOVE_HTML_TAG_ELEMENT',\n  CHANGE_DELETE_SPEED: 'CHANGE_DELETE_SPEED',\n  CHAN<PERSON>_DELAY: 'CHANGE_DELAY',\n  CHANGE_CURSOR: 'CHANGE_CURSOR',\n  PASTE_STRING: 'PASTE_STRING',\n};\n\nexport const VISIBLE_NODE_TYPES = {\n  HTML_TAG: 'HTML_TAG',\n  TEXT_NODE: 'TEXT_NODE',\n}\n\nexport const STYLES = `.Typewriter__cursor{-webkit-animation:Typewriter-cursor 1s infinite;animation:Typewriter-cursor 1s infinite;margin-left:1px}@-webkit-keyframes Typewriter-cursor{0%{opacity:0}50%{opacity:1}100%{opacity:0}}@keyframes Typewriter-cursor{0%{opacity:0}50%{opacity:1}100%{opacity:0}}`;", "import raf, { cancel as cancelRaf } from 'raf';\nimport {\n  doesStringContainHTMLTag,\n  getDOMElementFromString,\n  getRandomInteger,\n  addStyles,\n} from './../utils';\nimport {\n  EVENT_NAMES,\n  VISIBLE_NODE_TYPES,\n  STYLES,\n} from './constants';\n\nclass Typewriter {\n  state = {\n    cursorAnimation: null,\n    lastFrameTime: null,\n    pauseUntil: null,\n    eventQueue: [],\n    eventLoop: null,\n    eventLoopPaused: false,\n    reverseCalledEvents: [],\n    calledEvents: [],\n    visibleNodes: [],\n    initialOptions: null,\n    elements: {\n      container: null,\n      wrapper: document.createElement('span'),\n      cursor: document.createElement('span'),\n    },\n  }\n\n  options = {\n    strings: null,\n    cursor: '|',\n    delay: 'natural',\n    pauseFor: 1500,\n    deleteSpeed: 'natural',\n    loop: false,\n    autoStart: false,\n    devMode: false,\n    skipAddStyles: false,\n    wrapperClassName: 'Typewriter__wrapper',\n    cursorClassName: 'Typewriter__cursor',\n    stringSplitter: null,\n    onCreateTextNode: null,\n    onRemoveNode: null,\n  }\n\n  constructor(container, options) {\n    if(container) {\n      if(typeof container === 'string') {\n        const containerElement = document.querySelector(container);\n  \n        if(!containerElement) {\n          throw new Error('Could not find container element');\n        }\n  \n        this.state.elements.container = containerElement;\n      } else {\n        this.state.elements.container = container;\n      }\n    }\n\n    if(options) {\n      this.options = {\n        ...this.options,\n        ...options\n      };\n    }\n\n    // Make a copy of the options used to reset options when looping\n    this.state.initialOptions = { ...this.options };\n\n    this.init();\n  }\n\n  init() {\n    this.setupWrapperElement();\n    this.addEventToQueue(EVENT_NAMES.CHANGE_CURSOR, { cursor: this.options.cursor }, true);\n    this.addEventToQueue(EVENT_NAMES.REMOVE_ALL, null, true);\n\n    if(window && !window.___TYPEWRITER_JS_STYLES_ADDED___ && !this.options.skipAddStyles) {\n      addStyles(STYLES);\n      window.___TYPEWRITER_JS_STYLES_ADDED___ = true;\n    }\n\n    if(this.options.autoStart === true && this.options.strings) {\n      this.typeOutAllStrings().start();\n\t\t}\n  }\n\n  /**\n   * Replace all child nodes of provided element with\n   * state wrapper element used for typewriter effect\n   *\n   * <AUTHOR> Safi <<EMAIL>>\n   */\n  setupWrapperElement = () => {\n    if(!this.state.elements.container) {\n      return\n    }\n\n    this.state.elements.wrapper.className = this.options.wrapperClassName;\n    this.state.elements.cursor.className = this.options.cursorClassName;\n\n    this.state.elements.cursor.innerHTML = this.options.cursor;\n    this.state.elements.container.innerHTML = '';\n\n    this.state.elements.container.appendChild(this.state.elements.wrapper);\n    this.state.elements.container.appendChild(this.state.elements.cursor);\n  }\n\n  /**\n   * Start typewriter effect\n   */\n  start = () => {\n    this.state.eventLoopPaused = false;\n    this.runEventLoop();\n\n    return this;\n  }\n\n  /**\n   * Pause the event loop\n   *\n   * <AUTHOR> Safi <<EMAIL>>\n   */\n  pause = () => {\n    this.state.eventLoopPaused = true;\n\n    return this;\n  }\n\n  /**\n   * Destroy current running instance\n   *\n   * <AUTHOR> Safi <<EMAIL>>\n   */\n  stop = () => {\n    if(this.state.eventLoop) {\n      cancelRaf(this.state.eventLoop);\n      this.state.eventLoop = null;\n    }\n\n    return this;\n  }\n\n  /**\n   * Add pause event to queue for ms provided\n   *\n   * @param {Number} ms Time in ms to pause for\n   * @return {Typewriter}\n   *\n   * <AUTHOR> Safi <<EMAIL>>\n   */\n  pauseFor = (ms) => {\n    this.addEventToQueue(EVENT_NAMES.PAUSE_FOR, { ms });\n\n    return this;\n  }\n\n  /**\n   * Start typewriter effect by typing\n   * out all strings provided\n   *\n   * @return {Typewriter}\n   *\n   * <AUTHOR> Safi <<EMAIL>>\n   */\n  typeOutAllStrings = () => {\n    if(typeof this.options.strings === 'string') {\n      this.typeString(this.options.strings)\n        .pauseFor(this.options.pauseFor);\n      return this;\n    }\n\n    this.options.strings.forEach(string => {\n      this.typeString(string)\n        .pauseFor(this.options.pauseFor)\n        .deleteAll(this.options.deleteSpeed);\n    });\n\n    return this;\n  }\n\n  /**\n   * Adds string characters to event queue for typing\n   *\n   * @param {String} string String to type\n   * @param {HTMLElement} node Node to add character inside of\n   * @return {Typewriter}\n   *\n   * <AUTHOR> Safi <<EMAIL>>\n   */\n  typeString = (string, node = null) => {\n    if(doesStringContainHTMLTag(string)) {\n      return this.typeOutHTMLString(string, node);\n    }\n\n    if(string) {\n      const { stringSplitter } = this.options || {};\n      const characters = typeof stringSplitter === 'function' ? stringSplitter(string) : string.split('');\n      this.typeCharacters(characters, node);\n    }\n\n    return this;\n  }\n\n  /**\n   * Adds entire strings to event queue for paste effect\n   *\n   * @param {String} string String to paste\n   * @param {HTMLElement} node Node to add string inside of\n   * @return {Typewriter}\n   *\n   * <AUTHOR> Felicio <<EMAIL>>\n   */\n  pasteString = (string, node = null) => {\n    if(doesStringContainHTMLTag(string)) {\n      return this.typeOutHTMLString(string, node, true);\n    }\n\n    if(string) {\n      this.addEventToQueue(EVENT_NAMES.PASTE_STRING, { character: string, node });\n    }\n\n    return this;\n  }\n\n  /**\n   * Type out a string which is wrapper around HTML tag\n   *\n   * @param {String} string String to type\n   * @param {HTMLElement} parentNode Node to add inner nodes to\n   * @return {Typewriter}\n   *\n   * <AUTHOR> Safi <<EMAIL>>\n   */\n  typeOutHTMLString = (string, parentNode = null, pasteEffect) => {\n    const childNodes = getDOMElementFromString(string);\n\n    if(childNodes.length > 0 ) {\n      for(let i = 0; i < childNodes.length; i++) {\n        const node = childNodes[i];\n        const nodeHTML = node.innerHTML;\n\n        if(node && node.nodeType !== 3) {\n          // Reset innerText of HTML element\n          node.innerHTML = '';\n\n          // Add event queue item to insert HTML tag before typing characters\n          this.addEventToQueue(EVENT_NAMES.ADD_HTML_TAG_ELEMENT, {\n            node,\n            parentNode,\n          });\n\n            pasteEffect ? this.pasteString(nodeHTML, node) :  this.typeString(nodeHTML, node);\n        } else {\n          if(node.textContent) {\n            pasteEffect ? this.pasteString(node.textContent, parentNode) :  this.typeString(node.textContent, parentNode);\n          }\n        }\n      }\n    }\n\n    return this;\n  }\n\n  /**\n   * Add delete all characters to event queue\n   *\n   * @return {Typewriter}\n   *\n   * <AUTHOR> Safi <<EMAIL>>\n   */\n  deleteAll = (speed = 'natural') => {\n    this.addEventToQueue(EVENT_NAMES.REMOVE_ALL, { speed });\n    return this;\n  }\n\n  /**\n   * Change delete speed\n   *\n   * @param {Number} speed Speed to use for deleting characters\n   * @return {Typewriter}\n   *\n   * <AUTHOR> Safi <<EMAIL>>\n   */\n  changeDeleteSpeed = (speed) => {\n    if(!speed) {\n      throw new Error('Must provide new delete speed');\n    }\n\n    this.addEventToQueue(EVENT_NAMES.CHANGE_DELETE_SPEED, { speed });\n\n    return this;\n  }\n\n  /**\n   * Change delay when typing\n   *\n   * @param {Number} delay Delay when typing out characters\n   * @return {Typewriter}\n   *\n   * <AUTHOR> Safi <<EMAIL>>\n   */\n  changeDelay = (delay) => {\n    if(!delay) {\n      throw new Error('Must provide new delay');\n    }\n\n    this.addEventToQueue(EVENT_NAMES.CHANGE_DELAY, { delay });\n\n    return this;\n  }\n\n  /**\n   * Change cursor\n   *\n   * @param {String} character/string to represent as cursor\n   * @return {Typewriter}\n   *\n   * <AUTHOR> <<EMAIL>>\n   */\n  changeCursor = (cursor) => {\n    if(!cursor) {\n      throw new Error('Must provide new cursor');\n    }\n\n    this.addEventToQueue(EVENT_NAMES.CHANGE_CURSOR, { cursor });\n\n    return this;\n  }\n\n  /**\n   * Add delete character to event queue for amount of characters provided\n   *\n   * @param {Number} amount Number of characters to remove\n   * @return {Typewriter}\n   *\n   * <AUTHOR> Safi <<EMAIL>>\n   */\n  deleteChars = (amount) => {\n    if(!amount) {\n      throw new Error('Must provide amount of characters to delete');\n    }\n\n    for(let i = 0; i < amount; i++) {\n      this.addEventToQueue(EVENT_NAMES.REMOVE_CHARACTER);\n    }\n\n    return this;\n  }\n\n  /**\n   * Add an event item to call a callback function\n   *\n   * @param {cb}      cb        Callback function to call\n   * @param {Object}  thisArg   thisArg to use when calling function\n   * @return {Typewriter}\n   *\n   * <AUTHOR> Safi <<EMAIL>>\n   */\n  callFunction = (cb, thisArg) => {\n    if(!cb || typeof cb !== 'function') {\n      throw new Error('Callback must be a function');\n    }\n\n    this.addEventToQueue(EVENT_NAMES.CALL_FUNCTION, { cb, thisArg });\n\n    return this;\n  }\n\n  /**\n   * Add type character event for each character\n   *\n   * @param {Array} characters Array of characters\n   * @param {HTMLElement} node Node to add character inside of\n   * @return {Typewriter}\n   *\n   * <AUTHOR> Safi <<EMAIL>>\n   */\n  typeCharacters = (characters, node = null) => {\n    if(!characters || !Array.isArray(characters)) {\n      throw new Error('Characters must be an array');\n    }\n\n    characters.forEach(character => {\n      this.addEventToQueue(EVENT_NAMES.TYPE_CHARACTER, { character, node });\n    });\n\n    return this;\n  }\n\n  /**\n   * Add remove character event for each character\n   *\n   * @param {Array} characters Array of characters\n   * @return {Typewriter}\n   *\n   * <AUTHOR> Safi <<EMAIL>>\n   */\n  removeCharacters = (characters) => {\n    if(!characters || !Array.isArray(characters)) {\n      throw new Error('Characters must be an array');\n    }\n\n    characters.forEach(() => {\n      this.addEventToQueue(EVENT_NAMES.REMOVE_CHARACTER);\n    });\n\n    return this;\n  }\n\n  /**\n   * Add an event to the event queue\n   *\n   * @param {String}  eventName Name of the event\n   * @param {Object}  eventArgs Arguments to pass to event callback\n   * @param {Boolean} prepend   Prepend to begining of event queue\n   * @return {Typewriter}\n   *\n   * <AUTHOR> Safi <<EMAIL>>\n   */\n  addEventToQueue = (eventName, eventArgs, prepend = false) => {\n    return this.addEventToStateProperty(\n      eventName,\n      eventArgs,\n      prepend,\n      'eventQueue'\n    );\n  }\n\n  /**\n   * Add an event to reverse called events used for looping\n   *\n   * @param {String}  eventName Name of the event\n   * @param {Object}  eventArgs Arguments to pass to event callback\n   * @param {Boolean} prepend   Prepend to begining of event queue\n   * @return {Typewriter}\n   *\n   * <AUTHOR> Safi <<EMAIL>>\n   */\n  addReverseCalledEvent = (eventName, eventArgs, prepend = false) => {\n    const { loop } = this.options;\n\n    if(!loop) {\n      return this;\n    }\n\n    return this.addEventToStateProperty(\n      eventName,\n      eventArgs,\n      prepend,\n      'reverseCalledEvents'\n    );\n  }\n\n  /**\n   * Add an event to correct state property\n   *\n   * @param {String}  eventName Name of the event\n   * @param {Object}  eventArgs Arguments to pass to event callback\n   * @param {Boolean} prepend   Prepend to begining of event queue\n   * @param {String}  property  Property name of state object\n   * @return {Typewriter}\n   *\n   * <AUTHOR> Safi <<EMAIL>>\n   */\n  addEventToStateProperty = (eventName, eventArgs, prepend = false, property) => {\n    const eventItem = {\n      eventName,\n      eventArgs: eventArgs || {},\n    };\n\n    if(prepend) {\n      this.state[property] = [\n        eventItem,\n        ...this.state[property],\n      ];\n    } else {\n      this.state[property] = [\n        ...this.state[property],\n        eventItem,\n      ];\n    }\n\n    return this;\n  }\n\n  /**\n   * Run the event loop and do anything inside of the queue\n   *\n   * <AUTHOR> Safi <<EMAIL>>\n   */\n  runEventLoop = () => {\n    if(!this.state.lastFrameTime) {\n      this.state.lastFrameTime = Date.now();\n    }\n\n    // Setup variables to calculate if this frame should run\n    const nowTime = Date.now();\n    const delta = nowTime - this.state.lastFrameTime;\n\n    if(!this.state.eventQueue.length) {\n      if(!this.options.loop) {\n        return;\n      }\n\n      // Reset event queue if we are looping\n      this.state.eventQueue = [...this.state.calledEvents];\n      this.state.calledEvents = [];\n      this.options = {...this.state.initialOptions};\n    }\n\n    // Request next frame\n    this.state.eventLoop = raf(this.runEventLoop);\n\n    // Check if event loop is paused\n    if(this.state.eventLoopPaused) {\n      return;\n    }\n\n    // Check if state has pause until time\n    if(this.state.pauseUntil) {\n      // Check if event loop should be paused\n      if(nowTime < this.state.pauseUntil) {\n        return;\n      }\n\n      // Reset pause time\n      this.state.pauseUntil = null;\n    }\n\n    // Make a clone of event queue\n    const eventQueue = [...this.state.eventQueue];\n\n    // Get first event from queue\n    const currentEvent = eventQueue.shift();\n\n    // Setup delay variable\n    let delay = 0;\n\n    // Check if frame should run or be\n    // skipped based on fps interval\n    if(\n      currentEvent.eventName === EVENT_NAMES.REMOVE_LAST_VISIBLE_NODE ||\n      currentEvent.eventName === EVENT_NAMES.REMOVE_CHARACTER\n    ) {\n      delay = this.options.deleteSpeed === 'natural' ? getRandomInteger(40, 80) : this.options.deleteSpeed;\n    } else {\n      delay = this.options.delay === 'natural' ? getRandomInteger(120, 160) : this.options.delay;\n    }\n\n    if(delta <= delay) {\n      return;\n    }\n\n    // Get current event args\n    const { eventName, eventArgs } = currentEvent;\n\n    this.logInDevMode({ currentEvent, state: this.state, delay });\n\n    // Run item from event loop\n    switch(eventName) {\n      case EVENT_NAMES.PASTE_STRING:\n      case EVENT_NAMES.TYPE_CHARACTER: {\n        const { character, node } = eventArgs;\n        const textNode = document.createTextNode(character);\n\n        let textNodeToUse = textNode\n\n        if(this.options.onCreateTextNode && typeof this.options.onCreateTextNode === 'function') {\n          textNodeToUse = this.options.onCreateTextNode(character, textNode)\n        }\n\n        if(textNodeToUse) {\n          if(node) {\n            node.appendChild(textNodeToUse);\n          } else {\n            this.state.elements.wrapper.appendChild(textNodeToUse);\n          }\n        }\n\n        this.state.visibleNodes = [\n          ...this.state.visibleNodes,\n          {\n            type: VISIBLE_NODE_TYPES.TEXT_NODE,\n            character,\n            node: textNodeToUse,\n          },\n        ];\n\n        break;\n      }\n\n      case EVENT_NAMES.REMOVE_CHARACTER: {\n        eventQueue.unshift({\n          eventName: EVENT_NAMES.REMOVE_LAST_VISIBLE_NODE,\n          eventArgs: { removingCharacterNode: true },\n        });\n        break;\n      }\n\n      case EVENT_NAMES.PAUSE_FOR: {\n        const { ms } = currentEvent.eventArgs;\n        this.state.pauseUntil = Date.now() + parseInt(ms);\n        break;\n      }\n\n      case EVENT_NAMES.CALL_FUNCTION: {\n        const { cb, thisArg } = currentEvent.eventArgs;\n\n        cb.call(thisArg, {\n          elements: this.state.elements,\n        });\n\n        break;\n      }\n\n      case EVENT_NAMES.ADD_HTML_TAG_ELEMENT: {\n        const { node, parentNode } = currentEvent.eventArgs;\n\n        if(!parentNode) {\n          this.state.elements.wrapper.appendChild(node);\n        } else {\n          parentNode.appendChild(node);\n        }\n\n        this.state.visibleNodes = [\n          ...this.state.visibleNodes,\n          {\n            type: VISIBLE_NODE_TYPES.HTML_TAG,\n            node,\n            parentNode: parentNode || this.state.elements.wrapper,\n          },\n        ];\n        break;\n      }\n\n      case EVENT_NAMES.REMOVE_ALL: {\n        const { visibleNodes } = this.state;\n        const { speed } = eventArgs;\n        const removeAllEventItems = [];\n\n        // Change speed before deleteing\n        if(speed) {\n          removeAllEventItems.push({\n            eventName: EVENT_NAMES.CHANGE_DELETE_SPEED,\n            eventArgs: { speed, temp: true },\n          });\n        }\n\n        for(let i = 0, length = visibleNodes.length; i < length; i++) {\n          removeAllEventItems.push({\n            eventName: EVENT_NAMES.REMOVE_LAST_VISIBLE_NODE,\n            eventArgs: { removingCharacterNode: false },\n          });\n        }\n\n        // Change speed back to normal after deleteing\n        if(speed) {\n          removeAllEventItems.push({\n            eventName: EVENT_NAMES.CHANGE_DELETE_SPEED,\n            eventArgs: { speed: this.options.deleteSpeed, temp: true },\n          });\n        }\n\n        eventQueue.unshift(...removeAllEventItems);\n\n        break;\n      }\n\n      case EVENT_NAMES.REMOVE_LAST_VISIBLE_NODE: {\n        const { removingCharacterNode } = currentEvent.eventArgs;\n\n        if(this.state.visibleNodes.length) {\n          const { type, node, character } = this.state.visibleNodes.pop();\n\n          if(this.options.onRemoveNode && typeof this.options.onRemoveNode === 'function') {\n            this.options.onRemoveNode({\n              node,\n              character,\n            })\n          }\n\n          if(node) {\n            node.parentNode.removeChild(node);\n          }\n          \n          // Remove extra node as current deleted one is just an empty wrapper node\n          if(type === VISIBLE_NODE_TYPES.HTML_TAG && removingCharacterNode) {\n            eventQueue.unshift({\n              eventName: EVENT_NAMES.REMOVE_LAST_VISIBLE_NODE,\n              eventArgs: {},\n            });\n          }\n        }\n        break;\n      }\n\n      case EVENT_NAMES.CHANGE_DELETE_SPEED: {\n        this.options.deleteSpeed = currentEvent.eventArgs.speed;\n        break;\n      }\n\n      case EVENT_NAMES.CHANGE_DELAY: {\n        this.options.delay = currentEvent.eventArgs.delay;\n        break;\n      }\n\n      case EVENT_NAMES.CHANGE_CURSOR: {\n        this.options.cursor = currentEvent.eventArgs.cursor;\n        this.state.elements.cursor.innerHTML = currentEvent.eventArgs.cursor;\n        break;\n      }\n\n      default: {\n        break;\n      }\n    }\n\n    // Add que item to called queue if we are looping\n    if(this.options.loop) {\n      if(\n        currentEvent.eventName !== EVENT_NAMES.REMOVE_LAST_VISIBLE_NODE &&\n        !(currentEvent.eventArgs && currentEvent.eventArgs.temp)\n      ) {\n        this.state.calledEvents = [\n          ...this.state.calledEvents,\n          currentEvent\n        ];\n      }\n    }\n\n    // Replace state event queue with cloned queue\n    this.state.eventQueue = eventQueue;\n\n    // Set last frame time so it can be used to calculate next frame\n    this.state.lastFrameTime = nowTime;\n  }\n\n  /**\n   * Log a message in development mode\n   *\n   * @param {Mixed} message Message or item to console.log\n   * <AUTHOR> Safi <<EMAIL>>\n   */\n  logInDevMode(message) {\n    if(this.options.devMode) {\n      console.log(message);\n    }\n  }\n}\n\nexport default Typewriter;\n", "/**\n * Get the DOM element from a string\n * - Create temporary div element\n * - Change innerHTML of div element to the string\n * - Return the first child of the temporary div element\n * \n * @param {String} string String to convert into a DOM node\n * \n * <AUTHOR> <<EMAIL>>\n */\nconst getDOMElementFromString = (string) => {\n  const div = document.createElement('div');\n  div.innerHTML = string;\n  return div.childNodes;\n}\n\nexport default getDOMElementFromString;", "/**\n * Add styles to document head\n * \n * @param {String} styles CSS styles to add\n * @returns {void}\n */\nconst addStyles = (styles) => {\n  const styleBlock = document.createElement('style');\n  styleBlock.appendChild(document.createTextNode(styles));\n  document.head.appendChild(styleBlock);\n};\n\nexport default addStyles;"], "names": ["root", "factory", "exports", "module", "define", "amd", "self", "this", "now", "window", "g", "vendors", "suffix", "raf", "caf", "i", "length", "last", "id", "queue", "frameDuration", "callback", "_now", "next", "Math", "max", "setTimeout", "cp", "slice", "cancelled", "e", "round", "push", "handle", "fn", "call", "cancel", "apply", "arguments", "polyfill", "object", "requestAnimationFrame", "cancelAnimationFrame", "getNanoSeconds", "hrtime", "loadTime", "moduleLoadTime", "nodeLoadTime", "upTime", "performance", "process", "hr", "uptime", "Date", "getTime", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "__webpack_modules__", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "globalThis", "Function", "obj", "prop", "prototype", "hasOwnProperty", "string", "RegExp", "test", "min", "floor", "random", "EVENT_NAMES", "VISIBLE_NODE_TYPES", "Typewriter", "container", "options", "_this", "_classCallCheck", "_defineProperty", "cursorAnimation", "lastFrameTime", "pauseUntil", "eventQueue", "eventLoop", "eventLoopPaused", "reverseCalledEvents", "calledEvents", "visibleNodes", "initialOptions", "elements", "wrapper", "document", "createElement", "cursor", "strings", "delay", "pauseFor", "deleteSpeed", "loop", "autoStart", "devMode", "skipAdd<PERSON><PERSON>les", "wrapperClassName", "cursorClassName", "stringSplitter", "onCreateTextNode", "onRemoveNode", "state", "className", "innerHTML", "append<PERSON><PERSON><PERSON>", "runEventLoop", "cancelRaf", "ms", "addEventToQueue", "typeString", "for<PERSON>ach", "deleteAll", "node", "doesStringContainHTMLTag", "typeOutHTMLString", "characters", "split", "typeCharacters", "character", "parentNode", "pasteEffect", "childNodes", "div", "getDOMElementFromString", "nodeHTML", "nodeType", "pasteString", "textContent", "speed", "Error", "amount", "cb", "thisArg", "Array", "isArray", "eventName", "eventArgs", "prepend", "addEventToStateProperty", "property", "eventItem", "concat", "_toConsumableArray", "nowTime", "delta", "_objectSpread", "currentEvent", "shift", "getRandomInteger", "logInDevMode", "textNode", "createTextNode", "textNodeToUse", "type", "unshift", "removingCharacterNode", "parseInt", "_currentEvent$eventAr", "_currentEvent$eventAr2", "removeAllEventItems", "temp", "_this$state$visibleNo", "pop", "<PERSON><PERSON><PERSON><PERSON>", "containerElement", "querySelector", "init", "value", "styles", "styleBlock", "setupWrapperElement", "___TYPEWRITER_JS_STYLES_ADDED___", "head", "typeOutAllStrings", "start", "message", "console", "log"], "sourceRoot": ""}