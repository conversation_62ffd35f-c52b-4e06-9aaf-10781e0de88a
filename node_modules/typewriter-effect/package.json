{"name": "typewriter-effect", "version": "2.22.0", "main": "dist/react.js", "types": "index.d.ts", "keywords": ["typewriter", "typewriter effect", "es6", "react", "react typewriter", "react typewriter effect"], "files": ["dist", "index.d.ts", "LICENSE", "readme.md"], "repository": "**************:tameemsafi/typewriterjs.git", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "scripts": {"build:prod": "webpack --config ./webpack/production.config.babel.js", "build:dev": "webpack --config ./webpack/development.config.babel.js", "test": "jest --env=jsdom"}, "peerDependencies": {"react": ">=17.0.0", "react-dom": ">=17.0.0"}, "devDependencies": {"@babel/cli": "^7.21.5", "@babel/core": "^7.22.1", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/preset-env": "^7.22.4", "@babel/preset-react": "^7.22.3", "@babel/register": "^7.21.0", "@babel/runtime": "^7.22.3", "@testing-library/react": "^16.1.0", "babel-loader": "^8.3.0", "babel-plugin-transform-react-remove-prop-types": "^0.4.24", "core-js": "^3.30.2", "jest": "^28.1.3", "jest-environment-jsdom": "^28.1.3", "lolex": "^6.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "regenerator-runtime": "^0.13.11", "webpack": "^5.85.0", "webpack-bundle-analyzer": "^4.9.0", "webpack-cli": "^4.10.0", "webpack-merge": "^5.9.0"}, "dependencies": {"prop-types": "^15.8.1", "raf": "^3.4.1"}}