{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dev/ascenda/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport Image from \"next/image\";\nimport { useEffect, useRef, useState } from \"react\";\n\n// Hero Section Component\nfunction HeroSection() {\n  const sectionRef = useRef<HTMLElement>(null);\n  const scrollProgress = useScrollAnimation(sectionRef);\n\n  return (\n    <section ref={sectionRef} className=\"min-h-screen flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-gray-50 to-white\">\n      {/* Particle Effects Background */}\n      <div className=\"absolute inset-0 z-0\">\n        <div className=\"absolute right-0 top-1/2 -translate-y-1/2 w-1/2 h-full opacity-30\">\n          <Image\n            src=\"/particles.jpg\"\n            alt=\"Particle effects\"\n            fill\n            className=\"object-cover object-left\"\n            priority\n          />\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"container mx-auto px-6 lg:px-8 relative z-10\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Left Column - Text Content */}\n          <div className=\"space-y-8\">\n            <div className=\"space-y-5\">\n              <h1\n                className=\"text-5xl lg:text-2xl font-extralight text-gray-400 leading-tight transform transition-all duration-700 ease-out\"\n                style={{\n                  transform: `translateX(${(1 - scrollProgress) * 100}px)`,\n                  opacity: scrollProgress,\n                }}\n              >\n                Ascenda\n              </h1>\n              <h1\n                className=\"text-5xl lg:text-6xl font-medium text-gray-900 leading-tight transform transition-all duration-700 ease-out\"\n                style={{\n                  transform: `translateX(${(1 - scrollProgress) * 150}px)`,\n                  opacity: scrollProgress,\n                  transitionDelay: '200ms'\n                }}\n              >\n                Install the Growth Infrastructure\n              </h1>\n              <p\n                className=\"text-xl lg:text-2xl text-gray-700 leading-relaxed transform transition-all duration-700 ease-out\"\n                style={{\n                  transform: `translateX(${(1 - scrollProgress) * 200}px)`,\n                  opacity: scrollProgress,\n                  transitionDelay: '400ms'\n                }}\n              >\n                Your Business{\" \"}\n                <span className=\"font-extralight text-gray-400\">\n                  Has Been Missing\n                </span>\n              </p>\n            </div>\n          </div>\n\n          {/* Right Column - Visual Space for Particles */}\n          <div className=\"hidden lg:block relative\">\n            <div className=\"w-full h-96 relative\">\n              {/* This space is intentionally left for the particle effects background */}\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n\n// Plug and Pay Systems Section Component\nfunction PlugAndPaySection() {\n  const sectionRef = useRef<HTMLElement>(null);\n  const scrollProgress = useScrollAnimation(sectionRef);\n\n  return (\n    <section ref={sectionRef} className=\"min-h-screen bg-gray-100 flex items-center overflow-hidden\">\n      <div className=\"container mx-auto px-6 lg:px-8\">\n        <div className=\"grid lg:grid-cols-2 gap-8 lg:gap-12 items-start\">\n          {/* Left Column - Visual/Image Space */}\n          <div className=\"bg-blue-50 p-6 lg:p-8 h-180 flex items-center justify-center order-2 lg:order-1\">\n            {/* Placeholder for visual content */}\n          </div>\n\n          {/* Right Column - Content */}\n          <div className=\"space-y-6 order-1 lg:order-2\">\n            <div className=\"space-y-4\">\n              <p\n                className=\"text-sm font-medium pt-3 text-gray-500 uppercase tracking-wider transform transition-all duration-700 ease-out\"\n                style={{\n                  transform: `translateX(${(1 - scrollProgress) * 100}px)`,\n                  opacity: scrollProgress,\n                }}\n              >\n                Ascenda Partners builds\n              </p>\n              <h2\n                className=\"text-4xl lg:text-9xl font-medium text-gray-900 leading-tight transform transition-all duration-700 ease-out\"\n                style={{\n                  transform: `translateX(${(1 - scrollProgress) * 150}px)`,\n                  opacity: scrollProgress,\n                  // transitionDelay: '200ms'\n                }}\n              >\n                PLUG AND PAY SYSTEMS\n              </h2>\n            </div>\n\n            <div className=\"space-y-4\">\n              <p\n                className=\"text-lg text-gray-600 leading-relaxed transform transition-all duration-700 ease-out\"\n                style={{\n                  transform: `translateX(${(1 - scrollProgress) * 120}px)`,\n                  opacity: scrollProgress,\n                  // transitionDelay: '400ms'\n                }}\n              >\n                <span className=\"font-medium text-gray-900\">\n                  that help you generate leads, close more deals, and scale with\n                  precision\n                </span>\n              </p>\n              <p\n                className=\"text-base text-gray-600 italic transform transition-all duration-700 ease-out\"\n                style={{\n                  transform: `translateX(${(1 - scrollProgress) * 140}px)`,\n                  opacity: scrollProgress,\n                  // transitionDelay: '500ms'\n                }}\n              >\n                No fluff. No retainers. Just automated growth machines —\n                installed in days.\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n\n// Scroll Animation Hook\nfunction useScrollAnimation(ref: React.RefObject<HTMLElement>) {\n  const [scrollProgress, setScrollProgress] = useState(0);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      if (!ref.current) return;\n\n      const rect = ref.current.getBoundingClientRect();\n      const windowHeight = window.innerHeight;\n      const sectionHeight = rect.height;\n\n      // Calculate how much of the section is visible\n      const visibleTop = Math.max(0, windowHeight - rect.top);\n      const visibleBottom = Math.max(0, rect.bottom);\n      const visibleHeight = Math.min(visibleTop, visibleBottom, sectionHeight);\n\n      // Calculate scroll progress (0 to 1)\n      const progress = Math.min(Math.max(visibleHeight / sectionHeight, 0), 1);\n      setScrollProgress(progress);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    handleScroll(); // Initial call\n\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, [ref]);\n\n  return scrollProgress;\n}\n\n// Credibility/Promise Bar Section Component\nfunction CredibilitySection() {\n  const sectionRef = useRef<HTMLElement>(null);\n  const scrollProgress = useScrollAnimation(sectionRef);\n  const [currentIndex, setCurrentIndex] = useState(0);\n\n  const credibilityItems = [\n    \"Built by systems architects, not service providers\",\n    \"Powered by AI, automation, and clean workflows\",\n    \"Trusted by consultants, clinics, and scaling businesses\",\n    \"Installed in <14 days. Engineered for scale.\"\n  ];\n\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentIndex((prev) => (prev + 1) % credibilityItems.length);\n    }, 3000); // Change every 3 seconds\n\n    return () => clearInterval(interval);\n  }, [credibilityItems.length]);\n\n  return (\n    <section ref={sectionRef} className=\"py-12 bg-white border-t border-gray-200 overflow-hidden\">\n      <div className=\"container mx-auto px-6 lg:px-8\">\n        <div className=\"relative h-20 flex items-center justify-center\">\n          {/* Carousel Container */}\n          <div className=\"relative w-full max-w-4xl overflow-hidden\">\n            <div\n              className=\"flex transition-transform duration-700 ease-in-out\"\n              style={{\n                transform: `translateX(-${currentIndex * 100}%)`,\n              }}\n            >\n              {credibilityItems.map((item, index) => (\n                <div\n                  key={index}\n                  className=\"w-full flex-shrink-0 flex items-center justify-center px-4\"\n                >\n                  <p\n                    className=\"text-lg font-medium text-gray-900 text-center transform transition-all duration-500 ease-out\"\n                    style={{\n                      transform: `translateX(${(1 - scrollProgress) * 100}px)`,\n                      opacity: scrollProgress,\n                    }}\n                  >\n                    {item}\n                  </p>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Carousel Indicators */}\n          <div className=\"absolute bottom-0 left-1/2 transform -translate-x-1/2 flex space-x-2\">\n            {credibilityItems.map((_, index) => (\n              <button\n                key={index}\n                onClick={() => setCurrentIndex(index)}\n                className={`w-2 h-2 rounded-full transition-all duration-300 ${\n                  index === currentIndex\n                    ? 'bg-gray-900 w-6'\n                    : 'bg-gray-300 hover:bg-gray-400'\n                }`}\n              />\n            ))}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n\n// What We Do Section Component\nfunction WhatWeDoSection() {\n  const sectionRef = useRef<HTMLElement>(null);\n  const scrollProgress = useScrollAnimation(sectionRef);\n\n  const bulletPoints = [\n    { text: \"Generate qualified leads\", system: \"(Kairo)\" },\n    { text: \"Close more sales\", system: \"(Airo)\" },\n    { text: \"Deliver premium onboarding\", system: \"(Onboard)\" },\n    { text: \"Increase lifetime value\", system: \"(Nurture, Retain)\" }\n  ];\n\n  return (\n    <section ref={sectionRef} className=\"min-h-screen bg-gray-100 flex items-center overflow-hidden\">\n      <div className=\"container mx-auto px-6 lg:px-8\">\n        <div className=\"grid lg:grid-cols-2 gap-8 lg:gap-12 items-start\">\n          {/* Left Column - Content */}\n          <div className=\"space-y-6 order-1 lg:order-1\">\n            <div className=\"space-y-6\">\n              <p\n                className=\"text-sm pt-3 font-medium text-gray-500 uppercase tracking-wider transform transition-all duration-700 ease-out\"\n                style={{\n                  transform: `translateX(${(1 - scrollProgress) * 100}px)`,\n                  opacity: scrollProgress,\n                }}\n              >\n                We Don't Sell Time\n              </p>\n              <h2\n                className=\"text-4xl lg:text-7xl font-medium text-gray-900 leading-[1.4] transform transition-all duration-700 ease-out\"\n                style={{\n                  transform: `translateX(${(1 - scrollProgress) * 150}px)`,\n                  opacity: scrollProgress,\n                  // transitionDelay: '100ms'\n                }}\n              >\n                WE INSTALL SYSTEMS THAT MULTIPLY IT.\n              </h2>\n            </div>\n\n            <div className=\"space-y-4\">\n              <p\n                className=\"text-lg text-gray-600 leading-relaxed transform transition-all duration-700 ease-out\"\n                style={{\n                  transform: `translateX(${(1 - scrollProgress) * 120}px)`,\n                  opacity: scrollProgress,\n                  // transitionDelay: '400ms'\n                }}\n              >\n                At Ascenda, we don't do retainers, vague strategies, or service\n                packages.\n              </p>\n              <p\n                className=\"text-lg text-gray-600 leading-relaxed transform transition-all duration-700 ease-out\"\n                style={{\n                  transform: `translateX(${(1 - scrollProgress) * 140}px)`,\n                  opacity: scrollProgress,\n                  // transitionDelay: '500ms'\n                }}\n              >\n                We install modular business systems that are fully automated,\n                AI-powered, and designed to help you:\n              </p>\n            </div>\n\n            <div className=\"space-y-3 pl-4\">\n              {bulletPoints.map((point, index) => (\n                <div\n                  key={index}\n                  className=\"flex items-start gap-3 transform transition-all duration-700 ease-out\"\n                  style={{\n                    transform: `translateX(${(1 - scrollProgress) * 100}px)`,\n                    opacity: scrollProgress,\n                    // transitionDelay: `${600 + (index * 100)}ms`\n                  }}\n                >\n                  <span className=\"text-black font-bold\">•</span>\n                  <span className=\"text-base text-gray-700\">\n                    {point.text}{\" \"}\n                    <span className=\"font-medium\">{point.system}</span>\n                  </span>\n                </div>\n              ))}\n            </div>\n\n            <div className=\"space-y-4\">\n              <p\n                className=\"text-base text-gray-600 italic transform transition-all duration-700 ease-out\"\n                style={{\n                  transform: `translateX(${(1 - scrollProgress) * 80}px)`,\n                  opacity: scrollProgress,\n                  // transitionDelay: '1000ms'\n                }}\n              >\n                Each system runs in the background — like a revenue engine that\n                never stops.\n              </p>\n\n              <div className=\"pt-4\">\n                <button\n                  type=\"button\"\n                  className=\"bg-gray-900 cursor-pointer hover:bg-gray-800 text-white px-8 py-4 font-semibold text-lg transition-all duration-200 border-2 border-gray-900 hover:border-gray-800 transform\"\n                  style={{\n                    transform: `translateX(${(1 - scrollProgress) * 60}px)`,\n                    opacity: scrollProgress,\n                    // transitionDelay: '1200ms'\n                  }}\n                >\n                  Book a call and we'll show you what system fits you best\n                </button>\n              </div>\n            </div>\n          </div>\n\n          {/* Right Column - Visual Space */}\n          <div className=\"bg-blue-50 p-6 lg:p-8 h-180 flex items-center justify-center order-2 lg:order-2\">\n            {/* Placeholder for system diagram or visual */}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n\n// Featured System Section Component\nfunction FeaturedSystemSection() {\n  return (\n    <section className=\"py-20 bg-white overflow-hidden\">\n      <div className=\"container mx-auto px-6 lg:px-8\">\n        <div className=\"grid lg:grid-cols-2 gap-8 lg:gap-12\">\n          {/* KAIRO System Card */}\n          <div className=\"bg-gray-50 p-8 lg:p-10 space-y-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\"\n               style={{\n                 animation: 'parallaxFloat1 8s ease-in-out infinite'\n               }}>\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center gap-3\">\n                <h3 className=\"text-2xl lg:text-7xl font-medium text-gray-900\">\n                  KAIRO\n                </h3>\n              </div>\n              <h4 className=\"text-xl font-medium text-gray-900\">\n                Your Automated Growth Audit System\n              </h4>\n              <p className=\"text-base text-gray-600\">\n                Turn cold leads into warm calls using a self-diagnosing growth audit — powered by AI.\n              </p>\n            </div>\n\n            <div className=\"space-y-3\">\n              <div className=\"flex items-start gap-3\">\n                <span className=\"text-gray-900 font-bold\">•</span>\n                <span className=\"text-sm text-gray-700\">Free value first.</span>\n              </div>\n              <div className=\"flex items-start gap-3\">\n                <span className=\"text-gray-900 font-bold\">•</span>\n                <span className=\"text-sm text-gray-700\">High intent leads only.</span>\n              </div>\n              <div className=\"flex items-start gap-3\">\n                <span className=\"text-gray-900 font-bold\">•</span>\n                <span className=\"text-sm text-gray-700\">Pre-qualifies your pipeline.</span>\n              </div>\n            </div>\n\n            <div className=\"pt-4\">\n              <button\n                type=\"button\"\n                className=\"bg-gray-900 cursor-pointer hover:bg-gray-800 text-white px-6 py-3 font-semibold transition-all duration-200 border-2 border-gray-900 hover:border-gray-800\"\n              >\nRun Your Free Audit\n              </button>\n            </div>\n          </div>\n\n          {/* AIRO System Card */}\n          <div className=\"bg-gray-50 p-8 lg:p-10 space-y-6 text-right shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\"\n               style={{\n                 animation: 'parallaxFloat2 8s ease-in-out infinite 3s'\n               }}>\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center gap-3 justify-end\">\n                <h3 className=\"text-2xl lg:text-7xl font-medium text-gray-900\">\n                  AIRO\n                </h3>\n              </div>\n              <h4 className=\"text-xl font-medium text-gray-900\">\n                Your AI Sales Prep System\n              </h4>\n              <p className=\"text-base text-gray-600\">\n                Help your sales team close faster by letting AI prep every call, every time.\n              </p>\n            </div>\n\n            <div className=\"space-y-3\">\n              <div className=\"flex items-start gap-3 justify-end\">\n                <span className=\"text-sm text-gray-700\">Fully customized CRM logic</span>\n                <span className=\"text-gray-900 font-bold\">•</span>\n              </div>\n              <div className=\"flex items-start gap-3 justify-end\">\n                <span className=\"text-sm text-gray-700\">Competitor analysis</span>\n                <span className=\"text-gray-900 font-bold\">•</span>\n              </div>\n              <div className=\"flex items-start gap-3 justify-end\">\n                <span className=\"text-sm text-gray-700\">Objection profiles and deal notes auto-generated</span>\n                <span className=\"text-gray-900 font-bold\">•</span>\n              </div>\n            </div>\n\n            <div className=\"pt-4 flex justify-end\">\n              <button\n                type=\"button\"\n                className=\"bg-gray-900 cursor-pointer hover:bg-gray-800 text-white px-6 py-3 font-semibold transition-all duration-200 border-2 border-gray-900 hover:border-gray-800\"\n              >\n                See How Airo Works\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n\n// How It Works Section Component\nfunction HowItWorksSection() {\n  return (\n    <section className=\"py-20 bg-gray-100\">\n      <div className=\"container mx-auto px-6 lg:px-8\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl lg:text-7xl text-right font-medium text-gray-800 uppercase leading-tight mb-8\">\n            Built to Be Installed. Not Managed.\n          </h2>\n        </div>\n\n        <div className=\"grid lg:grid-cols-3 gap-8 lg:gap-12 mb-12\">\n          {/* Step 1 */}\n          <div className=\"space-y-4\">\n            <div className=\"bg-white p-8 space-y-4\">\n              <h3 className=\"text-xl font-bold text-gray-900\">Diagnose</h3>\n              <p className=\"text-base text-gray-600\">\n                We identify the best-fit system for your business.\n              </p>\n            </div>\n          </div>\n\n          {/* Step 2 */}\n          <div className=\"text-center space-y-4\">\n            <div className=\"bg-white p-8 space-y-4\">\n              <h3 className=\"text-xl font-bold text-gray-900\">Install</h3>\n              <p className=\"text-base text-gray-600\">\n                Our team builds and customizes it in under 2 weeks.\n              </p>\n            </div>\n          </div>\n\n          {/* Step 3 */}\n          <div className=\"text-center space-y-4\">\n            <div className=\"bg-white p-8 space-y-4 text-right\">\n              <h3 className=\"text-xl font-bold text-gray-900\">Run</h3>\n              <p className=\"text-base text-gray-600\">\n                The system operates automatically. You monitor the dashboard. We\n                handle the rest.\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"text-center \">\n          <button\n            type=\"button\"\n            className=\"bg-gray-900 cursor-pointer hover:bg-gray-800 text-white px-8 py-4 font-semibold text-lg transition-all duration-200 border-2 border-gray-900 hover:border-gray-800\"\n          >\n            Book Your System Assessment\n          </button>\n        </div>\n      </div>\n    </section>\n  );\n}\n\n// Why Ascenda Differentiator Section Component\nfunction WhyAscendaSection() {\n  return (\n    <section className=\"py-20 bg-white border-t border-gray-200\">\n      <div className=\"container mx-auto px-6 lg:px-8\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 mb-8\">\n            Ascenda vs. The Rest\n          </h2>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          <div className=\"text-center space-y-3\">\n            <div className=\"text-2xl text-green-600\">✔</div>\n            <h3 className=\"font-semibold text-gray-900\">Productized AI Systems</h3>\n          </div>\n\n          <div className=\"text-center space-y-3\">\n            <div className=\"text-2xl text-green-600\">✔</div>\n            <h3 className=\"font-semibold text-gray-900\">Built in days, not months</h3>\n          </div>\n\n          <div className=\"text-center space-y-3\">\n            <div className=\"text-2xl text-green-600\">✔</div>\n            <h3 className=\"font-semibold text-gray-900\">Designed to scale automatically</h3>\n          </div>\n\n          <div className=\"text-center space-y-3\">\n            <div className=\"text-2xl text-green-600\">✔</div>\n            <h3 className=\"font-semibold text-gray-900\">Boutique, high-end experience</h3>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n\n// Testimonials Section Component\nfunction TestimonialsSection() {\n  const sectionRef = useRef<HTMLElement>(null);\n  const scrollProgress = useScrollAnimation(sectionRef);\n\n  const testimonials = [\n    {\n      quote: \"Since we installed Airo, our close rate went up 33% — without hiring anyone new.\",\n      name: \"Sarah T.\",\n      title: \"Consultant, Dubai\"\n    },\n    {\n      quote: \"Kairo gave us 40 leads in 3 weeks — and 9 booked calls. All automated.\",\n      name: \"David R.\",\n      title: \"Agency Owner\"\n    }\n  ];\n\n  return (\n    <section ref={sectionRef} className=\"py-20 bg-gray-100 overflow-hidden\">\n      <div className=\"container mx-auto px-6 lg:px-8\">\n        <div className=\"grid lg:grid-cols-2 gap-12\">\n          {testimonials.map((testimonial, index) => (\n            <div\n              key={index}\n              className=\"bg-white p-8 lg:p-10 space-y-6 transform transition-all duration-700 ease-out\"\n              style={{\n                transform: `translateX(${(1 - scrollProgress) * (index === 0 ? 150 : -150)}px)`,\n                opacity: scrollProgress,\n                transitionDelay: `${index * 300}ms`\n              }}\n            >\n              <p className=\"text-lg text-gray-700 italic leading-relaxed\">\n                \"{testimonial.quote}\"\n              </p>\n              <div className=\"border-t pt-4\">\n                <p className=\"font-semibold text-gray-900\">{testimonial.name}</p>\n                <p className=\"text-sm text-gray-600\">{testimonial.title}</p>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n\n// Final CTA Section Component\nfunction FinalCTASection() {\n  return (\n    <section className=\"min-h-screen bg-gray-900 flex items-center\">\n      <div className=\"container mx-auto px-6 lg:px-8 text-center\">\n        <div className=\"max-w-4xl mx-auto space-y-8\">\n          <h2 className=\"text-4xl lg:text-6xl font-bold text-white leading-tight\">\n            Let's Turn Your Business Into a Machine\n          </h2>\n\n          <p className=\"text-xl text-gray-300 leading-relaxed max-w-3xl mx-auto\">\n            If you're tired of manual hustle, inconsistent leads, and bloated agencies…\n            It's time to install real growth infrastructure.\n          </p>\n\n          <div className=\"flex flex-col sm:flex-row gap-6 justify-center pt-8\">\n            <button\n              type=\"button\"\n              className=\"bg-white cursor-pointer hover:bg-gray-100 text-gray-900 px-8 py-4 font-semibold text-lg transition-all duration-200 border-2 border-white hover:border-gray-100\"\n            >\n             Run Your Free Growth Audit\n            </button>\n            <button\n              type=\"button\"\n              className=\"bg-transparent cursor-pointer hover:bg-white text-white hover:text-gray-900 px-8 py-4 font-semibold text-lg transition-all duration-200 border-2 border-white\"\n            >\n            Book a Discovery Call\n            </button>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n\nexport default function Home() {\n  return (\n    <main>\n      <HeroSection />\n      <PlugAndPaySection />\n      <CredibilitySection />\n      <WhatWeDoSection />\n      <FeaturedSystemSection />\n      <HowItWorksSection />\n      <WhyAscendaSection />\n      <TestimonialsSection />\n      <FinalCTASection />\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,yBAAyB;AACzB,SAAS;IACP,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAe;IACvC,MAAM,iBAAiB,mBAAmB;IAE1C,qBACE,8OAAC;QAAQ,KAAK;QAAY,WAAU;;0BAElC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,IAAI;wBACJ,WAAU;wBACV,QAAQ;;;;;;;;;;;;;;;;0BAMd,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,WAAU;wCACV,OAAO;4CACL,WAAW,CAAC,WAAW,EAAE,CAAC,IAAI,cAAc,IAAI,IAAI,GAAG,CAAC;4CACxD,SAAS;wCACX;kDACD;;;;;;kDAGD,8OAAC;wCACC,WAAU;wCACV,OAAO;4CACL,WAAW,CAAC,WAAW,EAAE,CAAC,IAAI,cAAc,IAAI,IAAI,GAAG,CAAC;4CACxD,SAAS;4CACT,iBAAiB;wCACnB;kDACD;;;;;;kDAGD,8OAAC;wCACC,WAAU;wCACV,OAAO;4CACL,WAAW,CAAC,WAAW,EAAE,CAAC,IAAI,cAAc,IAAI,IAAI,GAAG,CAAC;4CACxD,SAAS;4CACT,iBAAiB;wCACnB;;4CACD;4CACe;0DACd,8OAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;;;;;;sCAQtD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3B;AAEA,yCAAyC;AACzC,SAAS;IACP,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAe;IACvC,MAAM,iBAAiB,mBAAmB;IAE1C,qBACE,8OAAC;QAAQ,KAAK;QAAY,WAAU;kBAClC,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;;;;;kCAKf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,WAAU;wCACV,OAAO;4CACL,WAAW,CAAC,WAAW,EAAE,CAAC,IAAI,cAAc,IAAI,IAAI,GAAG,CAAC;4CACxD,SAAS;wCACX;kDACD;;;;;;kDAGD,8OAAC;wCACC,WAAU;wCACV,OAAO;4CACL,WAAW,CAAC,WAAW,EAAE,CAAC,IAAI,cAAc,IAAI,IAAI,GAAG,CAAC;4CACxD,SAAS;wCAEX;kDACD;;;;;;;;;;;;0CAKH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,WAAU;wCACV,OAAO;4CACL,WAAW,CAAC,WAAW,EAAE,CAAC,IAAI,cAAc,IAAI,IAAI,GAAG,CAAC;4CACxD,SAAS;wCAEX;kDAEA,cAAA,8OAAC;4CAAK,WAAU;sDAA4B;;;;;;;;;;;kDAK9C,8OAAC;wCACC,WAAU;wCACV,OAAO;4CACL,WAAW,CAAC,WAAW,EAAE,CAAC,IAAI,cAAc,IAAI,IAAI,GAAG,CAAC;4CACxD,SAAS;wCAEX;kDACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUf;AAEA,wBAAwB;AACxB,SAAS,mBAAmB,GAAiC;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,IAAI,CAAC,IAAI,OAAO,EAAE;YAElB,MAAM,OAAO,IAAI,OAAO,CAAC,qBAAqB;YAC9C,MAAM,eAAe,OAAO,WAAW;YACvC,MAAM,gBAAgB,KAAK,MAAM;YAEjC,+CAA+C;YAC/C,MAAM,aAAa,KAAK,GAAG,CAAC,GAAG,eAAe,KAAK,GAAG;YACtD,MAAM,gBAAgB,KAAK,GAAG,CAAC,GAAG,KAAK,MAAM;YAC7C,MAAM,gBAAgB,KAAK,GAAG,CAAC,YAAY,eAAe;YAE1D,qCAAqC;YACrC,MAAM,WAAW,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,gBAAgB,eAAe,IAAI;YACtE,kBAAkB;QACpB;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,gBAAgB,eAAe;QAE/B,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG;QAAC;KAAI;IAER,OAAO;AACT;AAEA,4CAA4C;AAC5C,SAAS;IACP,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAe;IACvC,MAAM,iBAAiB,mBAAmB;IAC1C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,mBAAmB;QACvB;QACA;QACA;QACA;KACD;IAED,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY;YAC3B,gBAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,iBAAiB,MAAM;QAChE,GAAG,OAAO,yBAAyB;QAEnC,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC,iBAAiB,MAAM;KAAC;IAE5B,qBACE,8OAAC;QAAQ,KAAK;QAAY,WAAU;kBAClC,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,WAAU;4BACV,OAAO;gCACL,WAAW,CAAC,YAAY,EAAE,eAAe,IAAI,EAAE,CAAC;4BAClD;sCAEC,iBAAiB,GAAG,CAAC,CAAC,MAAM,sBAC3B,8OAAC;oCAEC,WAAU;8CAEV,cAAA,8OAAC;wCACC,WAAU;wCACV,OAAO;4CACL,WAAW,CAAC,WAAW,EAAE,CAAC,IAAI,cAAc,IAAI,IAAI,GAAG,CAAC;4CACxD,SAAS;wCACX;kDAEC;;;;;;mCAVE;;;;;;;;;;;;;;;kCAkBb,8OAAC;wBAAI,WAAU;kCACZ,iBAAiB,GAAG,CAAC,CAAC,GAAG,sBACxB,8OAAC;gCAEC,SAAS,IAAM,gBAAgB;gCAC/B,WAAW,CAAC,iDAAiD,EAC3D,UAAU,eACN,oBACA,iCACJ;+BANG;;;;;;;;;;;;;;;;;;;;;;;;;;AAcrB;AAEA,+BAA+B;AAC/B,SAAS;IACP,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAe;IACvC,MAAM,iBAAiB,mBAAmB;IAE1C,MAAM,eAAe;QACnB;YAAE,MAAM;YAA4B,QAAQ;QAAU;QACtD;YAAE,MAAM;YAAoB,QAAQ;QAAS;QAC7C;YAAE,MAAM;YAA8B,QAAQ;QAAY;QAC1D;YAAE,MAAM;YAA2B,QAAQ;QAAoB;KAChE;IAED,qBACE,8OAAC;QAAQ,KAAK;QAAY,WAAU;kBAClC,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,WAAU;wCACV,OAAO;4CACL,WAAW,CAAC,WAAW,EAAE,CAAC,IAAI,cAAc,IAAI,IAAI,GAAG,CAAC;4CACxD,SAAS;wCACX;kDACD;;;;;;kDAGD,8OAAC;wCACC,WAAU;wCACV,OAAO;4CACL,WAAW,CAAC,WAAW,EAAE,CAAC,IAAI,cAAc,IAAI,IAAI,GAAG,CAAC;4CACxD,SAAS;wCAEX;kDACD;;;;;;;;;;;;0CAKH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,WAAU;wCACV,OAAO;4CACL,WAAW,CAAC,WAAW,EAAE,CAAC,IAAI,cAAc,IAAI,IAAI,GAAG,CAAC;4CACxD,SAAS;wCAEX;kDACD;;;;;;kDAID,8OAAC;wCACC,WAAU;wCACV,OAAO;4CACL,WAAW,CAAC,WAAW,EAAE,CAAC,IAAI,cAAc,IAAI,IAAI,GAAG,CAAC;4CACxD,SAAS;wCAEX;kDACD;;;;;;;;;;;;0CAMH,8OAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,OAAO,sBACxB,8OAAC;wCAEC,WAAU;wCACV,OAAO;4CACL,WAAW,CAAC,WAAW,EAAE,CAAC,IAAI,cAAc,IAAI,IAAI,GAAG,CAAC;4CACxD,SAAS;wCAEX;;0DAEA,8OAAC;gDAAK,WAAU;0DAAuB;;;;;;0DACvC,8OAAC;gDAAK,WAAU;;oDACb,MAAM,IAAI;oDAAE;kEACb,8OAAC;wDAAK,WAAU;kEAAe,MAAM,MAAM;;;;;;;;;;;;;uCAXxC;;;;;;;;;;0CAiBX,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,WAAU;wCACV,OAAO;4CACL,WAAW,CAAC,WAAW,EAAE,CAAC,IAAI,cAAc,IAAI,GAAG,GAAG,CAAC;4CACvD,SAAS;wCAEX;kDACD;;;;;;kDAKD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACV,OAAO;gDACL,WAAW,CAAC,WAAW,EAAE,CAAC,IAAI,cAAc,IAAI,GAAG,GAAG,CAAC;gDACvD,SAAS;4CAEX;sDACD;;;;;;;;;;;;;;;;;;;;;;;kCAQP,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;AAOzB;AAEA,oCAAoC;AACpC,SAAS;IACP,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;wBACV,OAAO;4BACL,WAAW;wBACb;;0CACH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAG,WAAU;sDAAiD;;;;;;;;;;;kDAIjE,8OAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAGlD,8OAAC;wCAAE,WAAU;kDAA0B;;;;;;;;;;;;0CAKzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAA0B;;;;;;0DAC1C,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;kDAE1C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAA0B;;;;;;0DAC1C,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;kDAE1C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAA0B;;;;;;0DAC1C,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAI5C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;kCAOL,8OAAC;wBAAI,WAAU;wBACV,OAAO;4BACL,WAAW;wBACb;;0CACH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAG,WAAU;sDAAiD;;;;;;;;;;;kDAIjE,8OAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAGlD,8OAAC;wCAAE,WAAU;kDAA0B;;;;;;;;;;;;0CAKzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;0DACxC,8OAAC;gDAAK,WAAU;0DAA0B;;;;;;;;;;;;kDAE5C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;0DACxC,8OAAC;gDAAK,WAAU;0DAA0B;;;;;;;;;;;;kDAE5C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;0DACxC,8OAAC;gDAAK,WAAU;0DAA0B;;;;;;;;;;;;;;;;;;0CAI9C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;AAEA,iCAAiC;AACjC,SAAS;IACP,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAG,WAAU;kCAAyF;;;;;;;;;;;8BAKzG,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAkC;;;;;;kDAChD,8OAAC;wCAAE,WAAU;kDAA0B;;;;;;;;;;;;;;;;;sCAO3C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAkC;;;;;;kDAChD,8OAAC;wCAAE,WAAU;kDAA0B;;;;;;;;;;;;;;;;;sCAO3C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAkC;;;;;;kDAChD,8OAAC;wCAAE,WAAU;kDAA0B;;;;;;;;;;;;;;;;;;;;;;;8BAQ7C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;;;;;;;;;;;AAOX;AAEA,+CAA+C;AAC/C,SAAS;IACP,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAG,WAAU;kCAAoD;;;;;;;;;;;8BAKpE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAA0B;;;;;;8CACzC,8OAAC;oCAAG,WAAU;8CAA8B;;;;;;;;;;;;sCAG9C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAA0B;;;;;;8CACzC,8OAAC;oCAAG,WAAU;8CAA8B;;;;;;;;;;;;sCAG9C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAA0B;;;;;;8CACzC,8OAAC;oCAAG,WAAU;8CAA8B;;;;;;;;;;;;sCAG9C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAA0B;;;;;;8CACzC,8OAAC;oCAAG,WAAU;8CAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMxD;AAEA,iCAAiC;AACjC,SAAS;IACP,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAe;IACvC,MAAM,iBAAiB,mBAAmB;IAE1C,MAAM,eAAe;QACnB;YACE,OAAO;YACP,MAAM;YACN,OAAO;QACT;QACA;YACE,OAAO;YACP,MAAM;YACN,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAQ,KAAK;QAAY,WAAU;kBAClC,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACZ,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,8OAAC;wBAEC,WAAU;wBACV,OAAO;4BACL,WAAW,CAAC,WAAW,EAAE,CAAC,IAAI,cAAc,IAAI,CAAC,UAAU,IAAI,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC;4BAC/E,SAAS;4BACT,iBAAiB,GAAG,QAAQ,IAAI,EAAE,CAAC;wBACrC;;0CAEA,8OAAC;gCAAE,WAAU;;oCAA+C;oCACxD,YAAY,KAAK;oCAAC;;;;;;;0CAEtB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAA+B,YAAY,IAAI;;;;;;kDAC5D,8OAAC;wCAAE,WAAU;kDAAyB,YAAY,KAAK;;;;;;;;;;;;;uBAbpD;;;;;;;;;;;;;;;;;;;;AAqBnB;AAEA,8BAA8B;AAC9B,SAAS;IACP,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0D;;;;;;kCAIxE,8OAAC;wBAAE,WAAU;kCAA0D;;;;;;kCAKvE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;AAEe,SAAS;IACtB,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC;;;;;0BACD,8OAAC;;;;;0BACD,8OAAC;;;;;0BACD,8OAAC;;;;;0BACD,8OAAC;;;;;0BACD,8OAAC;;;;;0BACD,8OAAC;;;;;0BACD,8OAAC;;;;;;;;;;;AAGP", "debugId": null}}]}